"""
GIS城市内涝灾害风险预警系统 - 工具函数
"""
import random
import string
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont
import base64


def generate_captcha_text(length=4):
    """生成验证码文本"""
    # 排除容易混淆的字符
    chars = '123456789ABCDEFGHJKLMNPQRSTUVWXYZ'
    return ''.join(random.choice(chars) for _ in range(length))


def create_captcha_image(text, width=120, height=40):
    """创建验证码图片"""
    # 创建图片
    image = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(image)

    # 尝试使用系统字体，如果失败则使用默认字体
    try:
        # Windows系统字体路径
        font = ImageFont.truetype('arial.ttf', 20)
    except:
        try:
            # Linux系统字体路径
            font = ImageFont.truetype('/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf', 20)
        except:
            # 使用默认字体
            font = ImageFont.load_default()

    # 绘制背景干扰线
    for _ in range(5):
        x1 = random.randint(0, width)
        y1 = random.randint(0, height)
        x2 = random.randint(0, width)
        y2 = random.randint(0, height)
        draw.line([(x1, y1), (x2, y2)], fill=random.choice(['gray', 'lightgray']), width=1)

    # 绘制干扰点
    for _ in range(50):
        x = random.randint(0, width)
        y = random.randint(0, height)
        draw.point((x, y), fill=random.choice(['gray', 'lightgray', 'darkgray']))

    # 绘制验证码文字
    text_width = draw.textlength(text, font=font) if hasattr(draw, 'textlength') else len(text) * 15
    text_height = 20

    x = (width - text_width) // 2
    y = (height - text_height) // 2

    # 为每个字符添加随机颜色和位置偏移
    char_width = text_width // len(text)
    for i, char in enumerate(text):
        char_x = x + i * char_width + random.randint(-3, 3)
        char_y = y + random.randint(-3, 3)
        color = random.choice(['black', 'darkblue', 'darkred', 'darkgreen'])
        draw.text((char_x, char_y), char, fill=color, font=font)

    return image


def captcha_to_base64(image):
    """将验证码图片转换为base64字符串"""
    buffer = BytesIO()
    image.save(buffer, format='PNG')
    buffer.seek(0)
    image_data = buffer.getvalue()
    base64_str = base64.b64encode(image_data).decode('utf-8')
    return f"data:image/png;base64,{base64_str}"


def generate_captcha():
    """生成完整的验证码（文本和图片）"""
    text = generate_captcha_text()
    image = create_captcha_image(text)
    image_base64 = captcha_to_base64(image)
    return text, image_base64


def validate_captcha(user_input, correct_text):
    """验证验证码"""
    if not user_input or not correct_text:
        return False
    return user_input.upper().strip() == correct_text.upper().strip()