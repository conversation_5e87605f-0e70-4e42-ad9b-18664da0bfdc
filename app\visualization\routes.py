"""
可视化模块路由
实现地图展示、图层叠加、图表分析功能

数据说明：
- 淹没范围数据：基于历史内涝数据和DEM地形分析生成的模拟数据
- 降雨量数据：结合气象历史数据和季节性模式生成的模拟数据
- 风险评估数据：根据地形、排水系统、历史内涝记录等综合因素计算
- 实际部署时可接入气象API和实时监测设备数据
"""
from flask import render_template, jsonify, request, current_app, send_file
from . import bp
from app.extensions import db
from .geo_boundaries import geo_boundaries_manager
from .pyecharts_maps import pyecharts_map_generator
import json
import random
from datetime import datetime, timedelta
import os
import io
import math
import pandas as pd

# 尝试导入PIL和numpy
try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    Image = None

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    np = None
except Exception:
    NUMPY_AVAILABLE = False
    np = None

# GIS库导入
try:
    import rasterio
    from rasterio.windows import Window
    from rasterio.warp import transform_bounds, reproject, Resampling
    from rasterio.crs import CRS
    GIS_LIBS_AVAILABLE = True and NUMPY_AVAILABLE and PIL_AVAILABLE
except ImportError:
    GIS_LIBS_AVAILABLE = False
    rasterio = None
except Exception:
    GIS_LIBS_AVAILABLE = False
    rasterio = None

# Mercantile库单独导入（用于地图瓦片处理）
try:
    import mercantile
    MERCANTILE_AVAILABLE = True
except ImportError:
    mercantile = None
    MERCANTILE_AVAILABLE = False
    print("Warning: mercantile library not available. Install with: pip install mercantile")
except Exception:
    mercantile = None
    MERCANTILE_AVAILABLE = False

from app.models import GISLayer


@bp.route('/')
def index():
    """可视化模块主页"""
    return render_template('visualization/index.html')


@bp.route('/map')
def map_view():
    """地图展示页面"""
    return render_template('visualization/map.html')

@bp.route('/debug-map')
def debug_map():
    """调试地图页面"""
    from flask import send_file
    import os
    debug_file = os.path.join(os.getcwd(), 'debug_map.html')
    return send_file(debug_file)


@bp.route('/pyecharts-maps')
def pyecharts_maps():
    """PyEcharts地图可视化页面"""
    return render_template('visualization/pyecharts_maps.html')


@bp.route('/charts')
def charts_view():
    """图表分析页面"""
    return render_template('visualization/charts.html')


@bp.route('/api/risk-layers')
def get_risk_layers():
    """获取风险分布图层数据"""
    city = request.args.get('city', 'zhengzhou')
    province = request.args.get('province')

    try:
        # 优先从数据库获取真实的风险数据
        risk_data = get_real_risk_data(city, province)

        # 如果没有真实数据，返回空数据
        if not risk_data:
            return jsonify({
                'success': False,
                'error': f'暂无 {province or city} 的风险数据，请先上传相关数据文件',
                'data': []
            })

        return jsonify({
            'success': True,
            'data': risk_data,
            'city': city,
            'province': province,
            'data_source': 'uploaded_data'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@bp.route('/api/administrative-layers')
def get_administrative_layers():
    """获取行政区划图层数据"""
    city = request.args.get('city', 'zhengzhou')
    province = request.args.get('province')

    try:
        # 优先从数据库获取真实的行政区划数据
        admin_data = get_real_administrative_data(city, province)

        # 如果没有真实数据，返回空数据
        if not admin_data:
            return jsonify({
                'success': False,
                'error': f'暂无 {province or city} 的行政区划数据，请先上传相关数据文件',
                'data': []
            })

        return jsonify({
            'success': True,
            'data': admin_data,
            'city': city,
            'province': province,
            'data_source': 'uploaded_data'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@bp.route('/api/terrain-layers')
def get_terrain_layers():
    """获取地形图层数据"""
    city = request.args.get('city', 'zhengzhou')
    province = request.args.get('province')

    try:
        # 优先从数据库获取真实的地形数据
        terrain_data = get_real_terrain_data(city, province)

        # 如果没有真实数据，返回空数据
        if not terrain_data:
            return jsonify({
                'success': False,
                'error': f'暂无 {province or city} 的地形数据，请先上传相关数据文件',
                'data': []
            })

        return jsonify({
            'success': True,
            'data': terrain_data,
            'city': city,
            'province': province,
            'data_source': 'uploaded_data'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@bp.route('/api/landuse-layers')
def get_landuse_layers():
    """获取土地利用图层数据"""
    city = request.args.get('city', 'zhengzhou')
    province = request.args.get('province')

    try:
        # 优先从数据库获取真实上传的土地利用数据
        landuse_data = get_real_landuse_data(city, province)

        # 如果没有真实数据，则生成示例数据（可选）
        if not landuse_data:
            # 注释掉虚拟数据生成，只返回空数据
            # landuse_data = generate_landuse_data(city)
            return jsonify({
                'success': False,
                'error': f'暂无 {province or city} 的土地利用数据，请先上传相关数据文件',
                'data': []
            })

        return jsonify({
            'success': True,
            'data': landuse_data,
            'city': city,
            'province': province,
            'data_source': 'uploaded_data'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@bp.route('/api/province-landuse-boundaries/<province_name>')
def get_province_landuse_boundaries(province_name):
    """获取省份土地利用边界数据，类似风险区划图层的实现"""
    try:
        from app.models import GISLayer

        # 获取年份参数（可选）
        year_filter = request.args.get('year', type=int)

        # 查询该省份的土地利用数据
        query = GISLayer.query.filter(
            GISLayer.data_category == 'landuse',
            GISLayer.processing_status == 'processed'
        )

        if province_name:
            query = query.filter(GISLayer.province == province_name)

        layers = query.all()

        if not layers:
            return jsonify({
                'success': False,
                'error': f'未找到省份 {province_name} 的土地利用数据'
            }), 404

        # 构建GeoJSON格式的边界数据
        features = []

        for layer in layers:
            try:
                # 处理矢量数据
                if layer.layer_type == 'vector' and layer.file_path:
                    vector_features = process_vector_landuse_to_geojson(layer)
                    features.extend(vector_features)

                # 处理栅格数据
                elif layer.layer_type == 'raster' and layer.file_path:
                    raster_features = process_raster_landuse_to_geojson(layer)
                    features.extend(raster_features)

            except Exception as e:
                print(f"处理图层 {layer.name} 失败: {e}")
                continue

        # 构建GeoJSON结构
        geojson_data = {
            'type': 'FeatureCollection',
            'features': features
        }

        return jsonify({
            'success': True,
            'province': province_name,
            'year': year_filter,
            'data': geojson_data,
            'feature_count': len(features),
            'message': f'成功加载 {province_name} 土地利用数据，共 {len(features)} 个区域'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取土地利用边界数据失败: {str(e)}'
        }), 500


@bp.route('/api/generate-pyecharts-landuse-map/<province_name>')
def generate_pyecharts_landuse_map(province_name):
    """生成基于PyEcharts的省份土地利用地图"""
    try:
        from app.models import GISLayer
        import os

        # 获取年份参数（可选）
        year_filter = request.args.get('year', type=int)

        print(f"开始生成 {province_name} 的土地利用PyEcharts地图，年份: {year_filter}")

        # 使用自动化处理器获取省份土地利用数据
        from app.services.landuse_auto_processor import landuse_auto_processor

        layers = landuse_auto_processor.get_province_landuse_layers(province_name)

        if not layers:
            return jsonify({
                'success': False,
                'error': f'未找到省份 {province_name} 的土地利用数据，请先通过数据导入页面上传TIFF格式的土地利用数据文件'
            }), 404

        print(f"找到 {len(layers)} 个土地利用图层")

        # 处理土地利用数据，转换为城市级别的数据
        city_landuse_data = []

        for layer in layers:
            try:
                print(f"处理图层: {layer.name}")

                # 检查文件是否存在
                if not layer.file_path or not os.path.exists(layer.file_path):
                    print(f"跳过图层 {layer.name}: 文件不存在 ({layer.file_path})")
                    # 为不存在的文件创建模拟数据
                    city_landuse_data.append({
                        'city_name': layer.province.replace('省', '') if layer.province else '未知',
                        'landuse_type': determine_landuse_type_from_filename(layer.name),
                        'area_km2': 1000.0,  # 模拟面积
                        'description': f'{layer.name}（模拟数据）',
                        'source': 'simulated_data'
                    })
                    continue

                # 处理矢量数据
                if layer.layer_type == 'vector' and layer.file_path:
                    vector_data = process_vector_landuse_data(layer, get_landuse_color_mapping())

                    # 转换为城市级别数据
                    for data in vector_data:
                        city_landuse_data.append({
                            'city_name': data.get('district', '未知'),
                            'landuse_type': data.get('landuse_type', '其他'),
                            'area_km2': data.get('area_km2', 0),
                            'description': data.get('description', ''),
                            'source': 'uploaded_data'
                        })

                # 处理栅格数据
                elif layer.layer_type == 'raster' and layer.file_path:
                    raster_data = process_raster_landuse_data(layer, get_landuse_color_mapping())

                    # 转换为城市级别数据
                    for data in raster_data:
                        city_landuse_data.append({
                            'city_name': data.get('district', '未知'),
                            'landuse_type': data.get('landuse_type', '其他'),
                            'area_km2': data.get('area_km2', 0),
                            'description': data.get('description', ''),
                            'source': 'uploaded_data'
                        })

            except Exception as e:
                print(f"处理图层 {layer.name} 失败: {e}")
                continue

        if not city_landuse_data:
            return jsonify({
                'success': False,
                'error': f'{province_name} 没有有效的土地利用数据'
            }), 404

        print(f"处理得到 {len(city_landuse_data)} 条城市土地利用数据")

        # 使用PyEcharts生成地图
        map_url = pyecharts_map_generator.generate_province_landuse_map(
            province_name,
            city_landuse_data,
            year_filter
        )

        if map_url:
            return jsonify({
                'success': True,
                'province': province_name,
                'year': year_filter,
                'map_url': map_url,
                'data_count': len(city_landuse_data),
                'message': f'成功生成 {province_name} 土地利用地图'
            })
        else:
            return jsonify({
                'success': False,
                'error': '地图生成失败，请检查PyEcharts配置'
            }), 500

    except Exception as e:
        print(f"生成土地利用地图失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'生成土地利用地图失败: {str(e)}'
        }), 500



@bp.route('/api/province-boundaries/<province_name>')
def get_province_boundaries(province_name):
    """获取省份地理边界数据，用于风险区划图的区域着色"""
    try:
        # 获取地理边界数据
        boundaries = geo_boundaries_manager.get_province_boundaries(province_name)

        if not boundaries or not boundaries.get('features'):
            return jsonify({
                'success': False,
                'error': f'未找到省份 {province_name} 的地理边界数据'
            }), 404

        return jsonify({
            'success': True,
            'province': province_name,
            'data': boundaries,
            'feature_count': len(boundaries.get('features', []))
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取地理边界数据失败: {str(e)}'
        }), 500


@bp.route('/api/province-risk-boundaries/<province_name>')
def get_province_risk_boundaries(province_name):
    """获取省份风险区划边界数据，包含风险等级信息"""
    try:
        from app.models import RiskAssessmentProject, RiskCalculationResult, RiskAssessmentData

        # 获取年份参数
        year_filter = request.args.get('year', type=int)

        # 获取地理边界数据
        boundaries = geo_boundaries_manager.get_province_boundaries(province_name)
        if not boundaries or not boundaries.get('features'):
            return jsonify({
                'success': False,
                'error': f'未找到省份 {province_name} 的地理边界数据'
            }), 404

        # 获取风险评估数据
        query = RiskAssessmentProject.query.filter(
            RiskAssessmentProject.location == province_name
        )

        if year_filter:
            query = query.filter(RiskAssessmentProject.assessment_year == year_filter)

        projects = query.all()

        # 如果没有指定年份的数据，获取最新年份的数据
        if not projects and not year_filter:
            latest_project = RiskAssessmentProject.query.filter(
                RiskAssessmentProject.location == province_name
            ).order_by(RiskAssessmentProject.assessment_year.desc()).first()

            if latest_project:
                projects = [latest_project]
                year_filter = latest_project.assessment_year

        # 构建城市风险等级映射
        city_risk_mapping = {}

        for project in projects:
            results = db.session.query(
                RiskCalculationResult, RiskAssessmentData
            ).join(
                RiskAssessmentData, RiskCalculationResult.data_id == RiskAssessmentData.id
            ).filter(
                RiskCalculationResult.project_id == project.id
            ).all()

            for result, data in results:
                city_name = data.location_name
                if city_name:
                    # 标准化城市名称，确保与边界数据中的名称匹配
                    standardized_name = city_name
                    if not standardized_name.endswith('市') and not standardized_name.endswith('县') and not standardized_name.endswith('区'):
                        standardized_name += '市'

                    # 确保风险等级有效
                    risk_level = result.final_risk_level
                    if not risk_level or risk_level == 'nan' or risk_level == 'None':
                        # 根据风险指数重新计算风险等级
                        from app.visualization.pyecharts_maps import PyEchartsMapGenerator
                        map_generator = PyEchartsMapGenerator()
                        risk_level = map_generator.get_risk_level_from_index(result.risk_index)

                    # 同时使用原始名称和标准化名称作为键，提高匹配成功率
                    risk_data = {
                        'risk_level': risk_level,
                        'risk_index': result.risk_index if result.risk_index is not None else 0,
                        'likelihood_probability': result.likelihood_probability if result.likelihood_probability is not None else 0,
                        'impact_degree': result.impact_degree if result.impact_degree is not None else 0,
                        'coordinates': data.coordinates,  # 保存坐标信息
                        'data_name': data.data_name
                    }

                    city_risk_mapping[city_name] = risk_data
                    if standardized_name != city_name:
                        city_risk_mapping[standardized_name] = risk_data

        # 为边界数据添加风险等级信息
        matched_cities = 0
        for feature in boundaries['features']:
            city_name = feature['properties']['name']

            # 尝试多种名称匹配方式
            risk_info = None
            for name_variant in [city_name, city_name + '市', city_name.replace('市', ''), city_name.replace('县', ''), city_name.replace('区', '')]:
                if name_variant in city_risk_mapping:
                    risk_info = city_risk_mapping[name_variant]
                    matched_cities += 1
                    break

            if not risk_info:
                risk_info = {}

            # 获取风险等级和颜色
            risk_level = risk_info.get('risk_level', '未评估')
            risk_index = risk_info.get('risk_index', 0)

            # 添加风险等级信息
            feature['properties'].update({
                'risk_level': risk_level,
                'risk_index': risk_index,
                'likelihood_probability': risk_info.get('likelihood_probability', 0),
                'impact_degree': risk_info.get('impact_degree', 0),
                'fill_color': geo_boundaries_manager.get_risk_level_color(risk_level),
                'fill_opacity': geo_boundaries_manager.get_risk_level_opacity(risk_level),
                'coordinates': risk_info.get('coordinates', ''),
                'data_name': risk_info.get('data_name', city_name)
            })

            # 调试信息
            color = geo_boundaries_manager.get_risk_level_color(risk_level)
            print(f"城市: {city_name}, 风险等级: {risk_level}, 风险指数: {risk_index}, 颜色: {color}")
            print(f"  - 匹配信息: risk_info存在={bool(risk_info)}, 原始数据={risk_info}")

        print(f"风险区划数据处理完成: 省份={province_name}, 年份={year_filter}, 总特征数={len(boundaries.get('features', []))}, 评估城市数={len(city_risk_mapping)}, 匹配城市数={matched_cities}")

        # 额外调试：显示所有可用的城市名称
        print(f"可用的风险评估城市: {list(city_risk_mapping.keys())}")
        print(f"边界数据中的城市: {[f['properties']['name'] for f in boundaries.get('features', [])]}")

        return jsonify({
            'success': True,
            'province': province_name,
            'year': year_filter,
            'data': boundaries,
            'feature_count': len(boundaries.get('features', [])),
            'assessed_cities': len(city_risk_mapping),
            'matched_cities': matched_cities,
            'message': f'成功加载 {province_name} 风险区划数据，共 {len(boundaries.get("features", []))} 个区域，其中 {matched_cities} 个区域有风险评估数据'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取风险区划边界数据失败: {str(e)}'
        }), 500


@bp.route('/api/generate-pyecharts-map/<province_name>')
def generate_pyecharts_map(province_name):
    """生成基于PyEcharts的省份风险地图"""
    try:
        from app.models import RiskAssessmentProject, RiskCalculationResult, RiskAssessmentData

        # 获取参数
        year_filter = request.args.get('year', type=int)
        map_type = request.args.get('type', 'map')  # map 或 heatmap

        print(f"生成PyEcharts地图请求: 省份={province_name}, 年份={year_filter}")

        # 首先查看该省份有哪些年份的数据
        available_years = db.session.query(RiskAssessmentProject.assessment_year).filter(
            RiskAssessmentProject.location == province_name,
            RiskAssessmentProject.assessment_year.isnot(None)
        ).distinct().order_by(RiskAssessmentProject.assessment_year.desc()).all()

        available_years_list = [year[0] for year in available_years]
        print(f"省份 {province_name} 可用年份: {available_years_list}")

        # 检查数据的data_name字段，看看是否与项目年份匹配
        sample_data = db.session.query(
            RiskAssessmentData.data_name,
            RiskAssessmentProject.assessment_year
        ).join(
            RiskAssessmentProject, RiskAssessmentData.project_id == RiskAssessmentProject.id
        ).filter(
            RiskAssessmentProject.location == province_name
        ).limit(5).all()

        print(f"样本数据检查:")
        for data_name, project_year in sample_data:
            print(f"  data_name: {data_name}, project_year: {project_year}")

        # 获取风险评估数据
        query = RiskAssessmentProject.query.filter(
            RiskAssessmentProject.location == province_name
        )

        if year_filter:
            query = query.filter(RiskAssessmentProject.assessment_year == year_filter)

        projects = query.all()

        # 如果没有找到数据的处理逻辑
        if not projects:
            if year_filter:
                # 指定了年份但没找到数据，返回明确的错误信息
                return jsonify({
                    'success': False,
                    'error': f'未找到省份 {province_name} {year_filter}年的风险评估数据'
                }), 404
            else:
                # 没有指定年份，尝试获取最新年份的数据
                latest_project = RiskAssessmentProject.query.filter(
                    RiskAssessmentProject.location == province_name
                ).order_by(RiskAssessmentProject.assessment_year.desc()).first()

                if latest_project:
                    projects = [latest_project]
                    year_filter = latest_project.assessment_year
                else:
                    return jsonify({
                        'success': False,
                        'error': f'未找到省份 {province_name} 的风险评估数据'
                    }), 404

        # 构建城市风险数据
        city_risk_data = []

        print(f"找到 {len(projects)} 个项目用于年份 {year_filter}")
        for project in projects:
            print(f"处理项目: ID={project.id}, 名称={project.project_name}, 年份={project.assessment_year}")

        for project in projects:
            results = db.session.query(
                RiskCalculationResult, RiskAssessmentData
            ).join(
                RiskAssessmentData, RiskCalculationResult.data_id == RiskAssessmentData.id
            ).filter(
                RiskCalculationResult.project_id == project.id
            ).all()

            print(f"项目 {project.id} 有 {len(results)} 个计算结果")

            for result, data in results:
                # 确保数据类型正确，避免NaN问题
                import math

                risk_index = result.risk_index
                if risk_index is None or (isinstance(risk_index, float) and math.isnan(risk_index)):
                    risk_index = 0.0
                else:
                    risk_index = float(risk_index)

                likelihood_prob = result.likelihood_probability
                if likelihood_prob is None or (isinstance(likelihood_prob, float) and math.isnan(likelihood_prob)):
                    likelihood_prob = 0.0
                else:
                    likelihood_prob = float(likelihood_prob)

                impact_deg = result.impact_degree
                if impact_deg is None or (isinstance(impact_deg, float) and math.isnan(impact_deg)):
                    impact_deg = 0.0
                else:
                    impact_deg = float(impact_deg)

                # 始终根据风险指数重新计算风险等级，确保数据一致性
                from app.visualization.pyecharts_maps import pyecharts_map_generator
                final_risk_level = pyecharts_map_generator.get_risk_level_from_index(risk_index)

                city_risk_data.append({
                    'city_name': data.location_name,
                    'risk_index': risk_index,
                    'risk_level': final_risk_level,
                    'likelihood_probability': likelihood_prob,
                    'impact_degree': impact_deg
                })

                print(f"添加城市数据: {data.location_name}, 风险指数: {risk_index:.4f}, 风险等级: {final_risk_level}, 数据名称: {data.data_name}")

        if not city_risk_data:
            return jsonify({
                'success': False,
                'error': f'省份 {province_name} 没有有效的风险评估数据'
            }), 404

        # 使用新的PyEcharts地图生成器
        from app.visualization.pyecharts_maps import pyecharts_map_generator

        map_url = pyecharts_map_generator.generate_province_risk_map_custom(
            province_name, city_risk_data, year_filter
        )

        if not map_url:
            return jsonify({
                'success': False,
                'error': '地图生成失败'
            }), 500

        # PyEcharts地图生成器返回的是相对于static的路径，直接使用
        map_url_accessible = map_url

        return jsonify({
            'success': True,
            'province': province_name,
            'year': year_filter,
            'map_type': map_type,
            'map_url': map_url_accessible,
            'city_count': len(city_risk_data),
            'city_risk_data': city_risk_data,  # 添加城市风险数据
            'data_summary': {
                'total_cities': len(city_risk_data),
                'avg_risk_index': sum(city['risk_index'] for city in city_risk_data) / len(city_risk_data) if city_risk_data else 0,
                'max_risk_index': max(city['risk_index'] for city in city_risk_data) if city_risk_data else 0,
                'min_risk_index': min(city['risk_index'] for city in city_risk_data) if city_risk_data else 0
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'生成PyEcharts地图失败: {str(e)}'
        }), 500


@bp.route('/api/generate-admin-map/<province_name>')
def generate_admin_map(province_name):
    """生成基于PyEcharts的省份市级行政区划地图"""
    try:
        from app.visualization.pyecharts_maps import pyecharts_map_generator

        print(f"生成行政区划地图请求: 省份={province_name}")

        # 生成行政区划地图
        map_url = pyecharts_map_generator.generate_province_admin_map(province_name)

        if not map_url:
            return jsonify({
                'success': False,
                'error': f'不支持的省份: {province_name}，或地图生成失败'
            }), 404

        # 验证文件是否存在
        from flask import current_app
        import os
        file_path = os.path.join(current_app.static_folder, map_url)
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': '地图文件生成失败'
            }), 500

        # 获取省份内的城市列表
        map_name = pyecharts_map_generator.province_map_names.get(province_name, province_name.replace('省', ''))
        cities = pyecharts_map_generator.province_cities.get(map_name, [])

        return jsonify({
            'success': True,
            'message': f'{province_name}行政区划地图生成成功',
            'province': province_name,
            'map_url': map_url,
            'city_count': len(cities),
            'cities': cities,
            'data_summary': {
                'total_cities': len(cities),
                'map_type': 'administrative_divisions'
            }
        })

    except Exception as e:
        print(f"生成行政区划地图失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'生成行政区划地图失败: {str(e)}'
        }), 500


@bp.route('/api/pyecharts-map/<filename>')
def serve_pyecharts_map(filename):
    """提供pyecharts生成的地图文件"""
    try:
        import tempfile
        import os
        from flask import send_file, make_response

        # 构建文件路径（从static/maps目录读取）
        from flask import current_app
        file_path = os.path.join(current_app.static_folder, 'maps', filename)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return "地图文件不存在", 404

        # 返回HTML文件，添加缓存控制头
        response = make_response(send_file(file_path, mimetype='text/html'))
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        return response

    except Exception as e:
        return f"获取地图文件失败: {str(e)}", 500


@bp.route('/api/landuse-image/<province_name>')
def get_landuse_image(province_name):
    """获取省份的土地利用TIFF图片"""
    try:
        from app.services.enhanced_landuse_processor import enhanced_landuse_processor

        # 获取省份的土地利用图层
        layers = enhanced_landuse_processor.get_province_landuse_layers(province_name)

        if not layers:
            return jsonify({
                'success': False,
                'error': f'未找到 {province_name} 的土地利用数据'
            }), 404

        # 使用第一个图层
        layer = layers[0]

        # 生成或获取TIFF图片
        image_url = enhanced_landuse_processor.generate_tiff_image(layer)

        if image_url:
            # 获取统计信息
            stats = enhanced_landuse_processor.analyze_landuse_statistics(layer)

            return jsonify({
                'success': True,
                'province': province_name,
                'image_url': image_url,
                'layer_name': layer.name,
                'statistics': stats,
                'message': f'成功获取 {province_name} 土地利用图片'
            })
        else:
            return jsonify({
                'success': False,
                'error': '生成土地利用图片失败'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取土地利用图片失败: {str(e)}'
        }), 500


@bp.route('/api/landuse-data/<province_name>')
def get_landuse_data(province_name):
    """获取省份的完整土地利用数据（图片+地图+统计）"""
    try:
        from app.services.enhanced_landuse_processor import enhanced_landuse_processor

        # 获取省份的土地利用图层
        layers = enhanced_landuse_processor.get_province_landuse_layers(province_name)

        if not layers:
            return jsonify({
                'success': False,
                'error': f'未找到 {province_name} 的土地利用数据'
            }), 404

        # 使用第一个图层
        layer = layers[0]

        # 获取或生成PNG图片（优先使用已生成的）
        image_url = enhanced_landuse_processor.get_or_generate_png_image(layer)

        # 生成PyEcharts地图
        map_url = enhanced_landuse_processor.generate_landuse_map(layer)

        # 获取统计信息
        stats = enhanced_landuse_processor.analyze_landuse_statistics(layer)

        # 获取图片的地理边界信息（用于地图叠加）
        bounds = None
        try:
            import rasterio
            with rasterio.open(layer.file_path) as src:
                bounds = src.bounds
                if src.crs and src.crs != 'EPSG:4326':
                    from rasterio.warp import transform_bounds
                    bounds = transform_bounds(src.crs, 'EPSG:4326', *bounds)
        except Exception as e:
            logger.warning(f"获取图片边界失败: {e}")

        return jsonify({
            'success': True,
            'province': province_name,
            'layer_name': layer.name,
            'image_url': image_url,
            'map_url': map_url,
            'statistics': stats,
            'data_count': len(stats.get('landuse_types', {})),
            'bounds': bounds,  # 图片的地理边界
            'message': f'成功获取 {province_name} 完整土地利用数据'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取土地利用数据失败: {str(e)}'
        }), 500


@bp.route('/api/landuse-png-layer/<province_name>')
def get_landuse_png_layer(province_name):
    """获取省份的土地利用PNG图层数据（用于地图叠加）"""
    try:
        from app.services.enhanced_landuse_processor import enhanced_landuse_processor

        # 获取省份的土地利用图层
        layers = enhanced_landuse_processor.get_province_landuse_layers(province_name)

        if not layers:
            return jsonify({
                'success': False,
                'error': f'未找到 {province_name} 的土地利用数据'
            }), 404

        # 使用第一个图层
        layer = layers[0]

        # 获取已生成的PNG图片（不重新生成）
        image_url = enhanced_landuse_processor.get_existing_png_image(layer)

        if not image_url:
            # 如果没有已生成的图片，尝试生成新的
            image_url = enhanced_landuse_processor.generate_tiff_image(layer)
            if image_url:
                # 保存到数据库
                enhanced_landuse_processor._save_png_metadata(layer, image_url)
            else:
                return jsonify({
                    'success': False,
                    'error': '获取PNG图片失败'
                }), 500

        # 获取图片的地理边界信息
        bounds = None
        try:
            import rasterio
            with rasterio.open(layer.file_path) as src:
                bounds = src.bounds
                if src.crs and src.crs != 'EPSG:4326':
                    from rasterio.warp import transform_bounds
                    bounds = transform_bounds(src.crs, 'EPSG:4326', *bounds)

                # 转换为[south, west, north, east]格式（Leaflet格式）
                bounds = [bounds[1], bounds[0], bounds[3], bounds[2]]
        except Exception as e:
            logger.warning(f"获取图片边界失败: {e}")
            # 使用河南省的大致边界作为备用
            bounds = [31.38, 110.35, 36.38, 116.65]  # [south, west, north, east]

        return jsonify({
            'success': True,
            'province': province_name,
            'layer_name': layer.name,
            'image_url': image_url,
            'bounds': bounds,
            'message': f'成功获取 {province_name} PNG图层数据'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取PNG图层数据失败: {str(e)}'
        }), 500


@bp.route('/api/province-risk-layer/<province_name>')
def get_province_risk_layer(province_name):
    """获取省份风险区划图层数据，用于Leaflet地图叠加"""
    try:
        from app.models import RiskAssessmentProject, RiskCalculationResult, RiskAssessmentData

        # 获取年份参数
        year_filter = request.args.get('year', type=int)

        # 获取地理边界数据
        boundaries = geo_boundaries_manager.get_province_boundaries(province_name)
        if not boundaries or not boundaries.get('features'):
            return jsonify({
                'success': False,
                'error': f'未找到省份 {province_name} 的地理边界数据'
            }), 404

        # 获取风险评估数据
        query = RiskAssessmentProject.query.filter(
            RiskAssessmentProject.location == province_name
        )

        if year_filter:
            query = query.filter(RiskAssessmentProject.assessment_year == year_filter)

        projects = query.all()

        # 如果没有指定年份的数据，获取最新年份的数据
        if not projects and not year_filter:
            latest_project = RiskAssessmentProject.query.filter(
                RiskAssessmentProject.location == province_name
            ).order_by(RiskAssessmentProject.assessment_year.desc()).first()

            if latest_project:
                projects = [latest_project]
                year_filter = latest_project.assessment_year

        # 构建城市风险等级映射
        city_risk_mapping = {}

        for project in projects:
            results = db.session.query(
                RiskCalculationResult, RiskAssessmentData
            ).join(
                RiskAssessmentData, RiskCalculationResult.data_id == RiskAssessmentData.id
            ).filter(
                RiskCalculationResult.project_id == project.id
            ).all()

            for result, data in results:
                city_name = data.location_name
                if city_name:
                    # 标准化城市名称
                    standardized_name = city_name
                    if not standardized_name.endswith('市') and not standardized_name.endswith('县') and not standardized_name.endswith('区'):
                        standardized_name += '市'

                    # 确保风险等级有效
                    risk_level = result.final_risk_level
                    if not risk_level or risk_level == 'nan' or risk_level == 'None':
                        # 根据风险指数重新计算风险等级
                        from app.visualization.pyecharts_maps import pyecharts_map_generator
                        risk_level = pyecharts_map_generator.get_risk_level_from_index(result.risk_index)

                    # 风险数据
                    risk_data = {
                        'risk_level': risk_level,
                        'risk_index': result.risk_index if result.risk_index is not None else 0,
                        'likelihood_probability': result.likelihood_probability if result.likelihood_probability is not None else 0,
                        'impact_degree': result.impact_degree if result.impact_degree is not None else 0,
                        'coordinates': data.coordinates,
                        'data_name': data.data_name
                    }

                    city_risk_mapping[city_name] = risk_data
                    if standardized_name != city_name:
                        city_risk_mapping[standardized_name] = risk_data

        # 为边界数据添加风险等级信息和样式
        matched_cities = 0
        for feature in boundaries['features']:
            city_name = feature['properties']['name']

            # 尝试多种名称匹配方式
            risk_info = None
            for name_variant in [city_name, city_name + '市', city_name.replace('市', ''), city_name.replace('县', ''), city_name.replace('区', '')]:
                if name_variant in city_risk_mapping:
                    risk_info = city_risk_mapping[name_variant]
                    matched_cities += 1
                    break

            if not risk_info:
                risk_info = {}

            # 获取风险等级和颜色
            risk_level = risk_info.get('risk_level', '未评估')
            risk_index = risk_info.get('risk_index', 0)

            # 添加风险等级信息和Leaflet样式
            feature['properties'].update({
                'risk_level': risk_level,
                'risk_index': risk_index,
                'likelihood_probability': risk_info.get('likelihood_probability', 0),
                'impact_degree': risk_info.get('impact_degree', 0),
                'fill_color': geo_boundaries_manager.get_risk_level_color(risk_level),
                'fill_opacity': 0.6,
                'stroke_color': '#ffffff',
                'stroke_weight': 2,
                'stroke_opacity': 0.8,
                'coordinates': risk_info.get('coordinates', ''),
                'data_name': risk_info.get('data_name', city_name)
            })

        return jsonify({
            'success': True,
            'province': province_name,
            'year': year_filter,
            'data': boundaries,
            'feature_count': len(boundaries.get('features', [])),
            'assessed_cities': len(city_risk_mapping),
            'matched_cities': matched_cities,
            'message': f'成功加载 {province_name} 风险区划图层数据'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取风险区划图层数据失败: {str(e)}'
        }), 500


@bp.route('/api/generate-china-overview-map')
def generate_china_overview_map():
    """生成全国风险概览地图"""
    try:
        try:
            from app.models import RiskAssessmentProject, RiskCalculationResult, RiskAssessmentData
        except ImportError:
            # 如果模型导入失败，返回模拟数据
            return generate_mock_china_overview_data()

        # 获取所有省份的风险评估数据
        provinces_query = db.session.query(
            RiskAssessmentProject.location,
            db.func.avg(RiskCalculationResult.risk_index).label('avg_risk_index'),
            db.func.count(RiskCalculationResult.id).label('assessment_count')
        ).join(
            RiskCalculationResult, RiskAssessmentProject.id == RiskCalculationResult.project_id
        ).group_by(
            RiskAssessmentProject.location
        ).all()

        if not provinces_query:
            return jsonify({
                'success': False,
                'error': '未找到任何风险评估数据'
            }), 404

        # 构建省份风险数据
        province_risk_data = []
        for province, avg_risk, count in provinces_query:
            province_risk_data.append({
                'province_name': province,
                'avg_risk_index': float(avg_risk or 0),
                'assessment_count': count
            })

        # 生成全国概览地图
        map_url = pyecharts_map_generator.generate_china_overview_map(province_risk_data)

        if not map_url:
            return jsonify({
                'success': False,
                'error': '全国概览地图生成失败'
            }), 500

        return jsonify({
            'success': True,
            'map_url': map_url,
            'province_count': len(province_risk_data),
            'data_summary': {
                'total_provinces': len(province_risk_data),
                'avg_risk_index': sum(p['avg_risk_index'] for p in province_risk_data) / len(province_risk_data),
                'total_assessments': sum(p['assessment_count'] for p in province_risk_data)
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'生成全国概览地图失败: {str(e)}'
        }), 500


@bp.route('/api/debug/database-status')
def debug_database_status():
    """调试端点：查看数据库中的项目和年份分布"""
    try:
        from app.models import RiskAssessmentProject, RiskCalculationResult, RiskAssessmentData

        # 查看所有项目
        projects = db.session.query(
            RiskAssessmentProject.id,
            RiskAssessmentProject.project_name,
            RiskAssessmentProject.location,
            RiskAssessmentProject.assessment_year,
            RiskAssessmentProject.status
        ).all()

        # 按省份和年份统计
        province_year_stats = {}
        for project in projects:
            province = project.location or '未知省份'
            year = project.assessment_year or '未知年份'

            if province not in province_year_stats:
                province_year_stats[province] = {}
            if year not in province_year_stats[province]:
                province_year_stats[province][year] = 0
            province_year_stats[province][year] += 1

        # 查看计算结果数量
        result_count = db.session.query(RiskCalculationResult).count()
        data_count = db.session.query(RiskAssessmentData).count()

        return jsonify({
            'success': True,
            'total_projects': len(projects),
            'total_results': result_count,
            'total_data': data_count,
            'projects': [
                {
                    'id': p.id,
                    'name': p.project_name,
                    'location': p.location,
                    'year': p.assessment_year,
                    'status': p.status
                } for p in projects
            ],
            'province_year_stats': province_year_stats
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'查看数据库状态失败: {str(e)}'
        }), 500


@bp.route('/api/debug/create-test-data')
def create_test_data():
    """创建不同年份的测试数据"""
    try:
        from app.models import RiskAssessmentProject, RiskAssessmentData, RiskCalculationResult
        from app.risk_assessment.risk_service import RiskAssessmentService
        import random

        risk_service = RiskAssessmentService()

        # 河南省的城市列表
        cities = ['郑州市', '洛阳市', '开封市', '南阳市', '安阳市', '商丘市', '新乡市', '平顶山市',
                 '许昌市', '焦作市', '周口市', '信阳市', '驻马店市', '濮阳市', '三门峡市', '漯河市', '鹤壁市', '济源市']

        created_projects = []

        # 为2015、2016、2017年创建测试数据
        for year in [2015, 2016, 2017]:
            # 检查是否已存在该年份的项目
            existing_project = RiskAssessmentProject.query.filter_by(
                location='河南省',
                assessment_year=year
            ).first()

            if existing_project:
                print(f"{year}年项目已存在，跳过")
                continue

            # 创建项目
            project = risk_service.create_assessment_project(
                project_name=f"河南省{year}年内涝风险评估",
                description=f"河南省各市{year}年内涝风险评估项目",
                location='河南省',
                assessment_year=year,
                user_id=1  # 假设管理员用户ID为1
            )

            # 为每个城市创建数据
            data_list = []
            for city in cities:
                # 生成随机但合理的数据，每年略有不同
                base_precipitation = 600 + random.uniform(-100, 100)
                year_factor = (year - 2014) * 0.1  # 年份因子

                data_dict = {
                    'data_name': f'{city}{year}年数据',
                    'location_name': city,
                    'annual_precipitation': base_precipitation + year_factor * random.uniform(-50, 50),
                    'drainage_network_density': random.uniform(0.1, 0.8),
                    'elevation': random.uniform(50, 500),
                    'population_density': random.uniform(100, 2000),
                    'gdp_per_area': random.uniform(50, 500),
                    'data_source': 'test_data',
                    'import_batch_id': f'test_{year}'
                }
                data_list.append(data_dict)

            # 添加数据到项目
            risk_service.add_assessment_data(project.id, data_list)

            # 计算风险
            risk_service.calculate_project_risks(project.id, user_id=1)

            created_projects.append({
                'year': year,
                'project_id': project.id,
                'data_count': len(data_list)
            })

            print(f"创建了{year}年的测试数据，项目ID: {project.id}")

        return jsonify({
            'success': True,
            'message': '测试数据创建完成',
            'created_projects': created_projects
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'创建测试数据失败: {str(e)}'
        }), 500



@bp.route('/api/charts/rainfall')
def get_rainfall_chart():
    """获取降雨量时间序列图表数据"""
    province = request.args.get('province', '河南省')
    city = request.args.get('city', '郑州市')
    year = request.args.get('year', type=int)

    try:
        # 优先使用真实数据库数据
        chart_data = generate_real_rainfall_chart_data(province, city, year)
        if chart_data:
            return jsonify({
                'success': True,
                'data': chart_data,
                'province': province,
                'city': city,
                'year': year,
                'data_source': 'database'
            })
        else:
            # 如果没有真实数据，返回错误信息
            return jsonify({
                'success': False,
                'error': f'未找到{province}{city}{year}年的降雨量数据',
                'data_source': 'none'
            })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@bp.route('/api/charts/drainage-density')
def get_drainage_density_chart_data():
    """获取排水管道密度图表数据"""
    province = request.args.get('province', '河南省')
    city = request.args.get('city', '郑州市')
    year = request.args.get('year', type=int)

    try:
        # 优先使用真实数据库数据
        drainage_data = generate_real_drainage_density_chart_data(province, city, year)
        if drainage_data:
            return jsonify({
                'success': True,
                'data': drainage_data,
                'province': province,
                'city': city,
                'year': year,
                'data_source': 'database'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'未找到{province}{city}{year}年的排水管密度数据',
                'data_source': 'none'
            })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@bp.route('/api/charts/population-density')
def get_population_density_chart_data():
    """获取人口密度图表数据"""
    province = request.args.get('province', '河南省')
    city = request.args.get('city', '郑州市')
    year = request.args.get('year', type=int)

    try:
        # 优先使用真实数据库数据
        population_data = generate_real_population_density_chart_data(province, city, year)
        if population_data:
            return jsonify({
                'success': True,
                'data': population_data,
                'province': province,
                'city': city,
                'year': year,
                'data_source': 'database'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'未找到{province}{city}{year}年的人口密度数据',
                'data_source': 'none'
            })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@bp.route('/api/charts/gdp-density')
def get_gdp_density_chart_data():
    """获取GDP密度图表数据"""
    province = request.args.get('province', '河南省')
    city = request.args.get('city', '郑州市')
    year = request.args.get('year', type=int)

    try:
        # 优先使用真实数据库数据
        gdp_data = generate_real_gdp_density_chart_data(province, city, year)
        if gdp_data:
            return jsonify({
                'success': True,
                'data': gdp_data,
                'province': province,
                'city': city,
                'year': year,
                'data_source': 'database'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'未找到{province}{city}{year}年的GDP密度数据',
                'data_source': 'none'
            })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@bp.route('/api/charts/average-elevation')
def get_average_elevation_chart_data():
    """获取平均高程图表数据"""
    province = request.args.get('province', '河南省')
    city = request.args.get('city', '郑州市')

    try:
        # 优先使用真实数据库数据
        elevation_data = generate_real_average_elevation_chart_data(province, city)
        if elevation_data:
            return jsonify({
                'success': True,
                'data': elevation_data,
                'province': province,
                'city': city,
                'data_source': 'database'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'未找到{province}{city}的平均高程数据',
                'data_source': 'none'
            })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@bp.route('/api/charts/elevation')
def get_elevation_chart_data():
    """获取高程数据图表数据"""
    province = request.args.get('province', '河南省')
    city = request.args.get('city', '郑州市')
    year = request.args.get('year', type=int)

    try:
        # 优先使用真实数据库数据
        elevation_data = generate_real_elevation_chart_data(province, city, year)
        if elevation_data:
            return jsonify({
                'success': True,
                'data': elevation_data,
                'province': province,
                'city': city,
                'year': year,
                'data_source': 'database'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'未找到{province}{city}{year}年的高程数据',
                'data_source': 'none'
            })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@bp.route('/api/charts/available-years')
def get_available_years():
    """获取图表分析可用的年份列表"""
    province = request.args.get('province', '河南省')
    city = request.args.get('city')

    try:
        from app.models import RiskAssessmentProject

        # 构建查询条件
        query = RiskAssessmentProject.query.filter(
            RiskAssessmentProject.location == province,
            RiskAssessmentProject.assessment_year.isnot(None)
        )

        # 如果指定了城市，可以进一步筛选（这里假设项目名称包含城市名）
        if city:
            query = query.filter(RiskAssessmentProject.project_name.like(f'%{city}%'))

        # 获取所有可用年份
        years = db.session.query(RiskAssessmentProject.assessment_year).filter(
            RiskAssessmentProject.location == province,
            RiskAssessmentProject.assessment_year.isnot(None)
        ).distinct().order_by(RiskAssessmentProject.assessment_year.desc()).all()

        year_list = [year[0] for year in years if year[0] is not None]

        # 获取最新年份
        latest_year = year_list[0] if year_list else None

        return jsonify({
            'success': True,
            'province': province,
            'city': city,
            'years': year_list,
            'latest_year': latest_year,
            'count': len(year_list)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取可用年份失败: {str(e)}'
        })


@bp.route('/api/districts/<city>')
def get_city_districts(city):
    """获取指定城市的区域列表"""
    try:
        # 优先从数据库获取真实的行政区划数据
        admin_data = get_real_administrative_data(city, None)

        # 如果没有真实数据，返回空数据
        if not admin_data:
            return jsonify({
                'success': False,
                'error': f'暂无 {city} 的行政区划数据，请先上传相关数据文件',
                'data': []
            })

        # 简化数据只返回必要信息
        districts_list = []
        for district in admin_data:
            districts_list.append({
                'id': district['name'].replace('区', '').replace('市', '').replace('县', ''),  # 移除行政级别后缀作为ID
                'name': district['name'],
                'center': district.get('center', [0, 0]),
                'area': district.get('area_km2', 0),
                'population': district.get('population', 0),
                'risk_level': district.get('flood_risk_level', '未知'),
                'risk_score': district.get('risk_score', 0)
            })

        return jsonify({
            'success': True,
            'data': districts_list,
            'city': city,
            'total': len(districts_list)
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@bp.route('/api/gis-layer-tiles/<int:layer_id>/<int:z>/<int:x>/<int:y>.png')
def get_gis_layer_tile(layer_id, z, x, y):
    """获取GIS图层瓦片数据（用于栅格图层）"""
    try:
        from app.models import GISLayer
        import rasterio
        from rasterio.warp import transform_bounds
        from rasterio.windows import from_bounds
        import numpy as np
        from PIL import Image
        import io
        import math

        # 获取图层信息
        layer = GISLayer.query.get_or_404(layer_id)

        if layer.layer_type != 'raster':
            return create_empty_tile()

        if not os.path.exists(layer.file_path):
            return create_empty_tile()

        # 计算瓦片边界（Web Mercator）
        def tile_bounds(x, y, z):
            """计算瓦片的地理边界"""
            n = 2.0 ** z
            lon_deg_min = x / n * 360.0 - 180.0
            lat_rad_max = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
            lat_deg_max = math.degrees(lat_rad_max)
            lon_deg_max = (x + 1) / n * 360.0 - 180.0
            lat_rad_min = math.atan(math.sinh(math.pi * (1 - 2 * (y + 1) / n)))
            lat_deg_min = math.degrees(lat_rad_min)
            return lon_deg_min, lat_deg_min, lon_deg_max, lat_deg_max

        tile_bbox = tile_bounds(x, y, z)

        # 读取栅格数据
        with rasterio.open(layer.file_path) as src:
            # 将瓦片边界转换到栅格坐标系
            if src.crs != 'EPSG:3857':  # Web Mercator
                from rasterio.warp import transform_bounds as warp_bounds
                tile_bbox_src = warp_bounds('EPSG:4326', src.crs, *tile_bbox)
            else:
                tile_bbox_src = tile_bbox

            # 计算窗口
            try:
                window = from_bounds(*tile_bbox_src, src.transform)

                # 读取数据
                data = src.read(1, window=window, out_shape=(256, 256))

                # 处理无数据值
                if src.nodata is not None:
                    data = np.where(data == src.nodata, 0, data)

                # 归一化到0-255范围
                if data.max() > data.min():
                    data_norm = ((data - data.min()) / (data.max() - data.min()) * 255).astype(np.uint8)
                else:
                    data_norm = np.zeros_like(data, dtype=np.uint8)

                # 创建图像
                if layer.data_category == 'landuse':
                    # 土地利用数据使用分类颜色
                    img = create_landuse_image(data_norm)
                else:
                    # 其他数据使用灰度图
                    img = Image.fromarray(data_norm, mode='L')
                    img = img.convert('RGBA')

                # 返回PNG图像
                img_io = io.BytesIO()
                img.save(img_io, 'PNG')
                img_io.seek(0)

                return send_file(img_io, mimetype='image/png')

            except Exception as e:
                print(f"处理瓦片数据失败: {e}")
                return create_empty_tile()

    except Exception as e:
        print(f"获取GIS图层瓦片失败: {e}")
        return create_empty_tile()


def create_landuse_image(data):
    """为土地利用数据创建彩色图像"""
    from PIL import Image
    import numpy as np

    # 土地利用类型颜色映射（简化版）
    colors = {
        0: (0, 0, 0, 0),        # 无数据 - 透明
        1: (0, 100, 0, 255),    # 森林 - 深绿
        2: (144, 238, 144, 255), # 草地 - 浅绿
        3: (255, 255, 0, 255),   # 农田 - 黄色
        4: (139, 69, 19, 255),   # 裸地 - 棕色
        5: (0, 0, 255, 255),     # 水体 - 蓝色
        6: (128, 128, 128, 255), # 建设用地 - 灰色
    }

    # 创建RGBA图像
    height, width = data.shape
    rgba_data = np.zeros((height, width, 4), dtype=np.uint8)

    for value, color in colors.items():
        mask = data == value
        rgba_data[mask] = color

    # 对于未定义的值，使用默认颜色
    undefined_mask = ~np.isin(data, list(colors.keys()))
    rgba_data[undefined_mask] = (200, 200, 200, 255)  # 浅灰色

    return Image.fromarray(rgba_data, 'RGBA')


def process_vector_landuse_data(layer, landuse_colors):
    """处理矢量土地利用数据"""
    try:
        # 检查是否有GIS库
        try:
            import geopandas as gpd
        except ImportError:
            print("GeoPandas不可用，无法处理矢量数据")
            return []

        # 读取Shapefile
        gdf = gpd.read_file(layer.file_path)

        # 确保使用WGS84坐标系
        try:
            # 检查坐标系是否有效
            if gdf.crs is None or str(gdf.crs) == 'EPSG:7' or 'EPSG:7' in str(gdf.crs):
                print(f"警告: 检测到无效坐标系 {gdf.crs}，假设为 EPSG:4326")
                gdf.crs = 'EPSG:4326'
            elif gdf.crs != 'EPSG:4326':
                gdf = gdf.to_crs('EPSG:4326')
        except Exception as e:
            print(f"坐标系转换失败: {e}，假设为 EPSG:4326")
            gdf.crs = 'EPSG:4326'

        landuse_data = []

        for idx, row in gdf.iterrows():
            try:
                # 获取几何对象
                geom = row.geometry

                # 确定土地利用类型
                landuse_type = determine_landuse_type(row, layer)

                # 获取颜色
                color = landuse_colors.get(landuse_type, landuse_colors.get('其他', '#DDA0DD'))

                # 转换几何为坐标列表
                coordinates = []
                if geom.geom_type == 'Polygon':
                    # 获取外环坐标
                    coords = list(geom.exterior.coords)
                    coordinates = [[lat, lng] for lng, lat in coords]
                elif geom.geom_type == 'MultiPolygon':
                    # 处理多边形，取第一个
                    if len(geom.geoms) > 0:
                        coords = list(geom.geoms[0].exterior.coords)
                        coordinates = [[lat, lng] for lng, lat in coords]

                if coordinates:
                    # 计算面积（粗略估算）
                    area_km2 = calculate_polygon_area(coordinates)

                    landuse_data.append({
                        'landuse_type': landuse_type,
                        'coordinates': coordinates,
                        'color': color,
                        'area_km2': round(area_km2, 2),
                        'description': f'{landuse_type}区域',
                        'district': layer.province or '未知',
                        'source': 'uploaded_data'
                    })

            except Exception as e:
                print(f"处理要素 {idx} 失败: {e}")
                continue

        return landuse_data

    except Exception as e:
        print(f"处理矢量土地利用数据失败: {e}")
        return []


def process_raster_landuse_data(layer, landuse_colors):
    """处理栅格土地利用数据（真实数据分析版本）"""
    try:
        # 检查文件是否存在
        if not layer.file_path or not os.path.exists(layer.file_path):
            print(f"栅格文件不存在: {layer.file_path}")
            return []

        # 尝试使用rasterio读取栅格数据
        try:
            import rasterio
            import numpy as np

            print(f"正在分析栅格文件: {layer.file_path}")

            with rasterio.open(layer.file_path) as src:
                # 读取栅格数据的基本信息
                bounds = src.bounds
                width = src.width
                height = src.height

                print(f"栅格信息: {width}x{height} 像素")

                # 转换边界到WGS84坐标系
                if src.crs and src.crs != 'EPSG:4326':
                    from rasterio.warp import transform_bounds
                    bounds = transform_bounds(src.crs, 'EPSG:4326', *bounds)

                minx, miny, maxx, maxy = bounds

                # 读取较大的样本数据来分析土地利用类型分布
                sample_size = min(500, width // 4, height // 4)
                print(f"读取样本数据: {sample_size}x{sample_size} 像素")

                # 从中心区域读取样本
                col_start = width // 2 - sample_size // 2
                row_start = height // 2 - sample_size // 2
                window = rasterio.windows.Window(col_start, row_start, sample_size, sample_size)
                sample_data = src.read(1, window=window)

                # 分析样本数据中的唯一值和分布
                valid_data = sample_data[sample_data != src.nodata] if src.nodata is not None else sample_data
                unique_values, counts = np.unique(valid_data, return_counts=True)

                print(f"发现 {len(unique_values)} 个唯一值: {unique_values}")

                # CLCD土地利用分类编码映射
                clcd_mapping = {
                    1: '耕地',      # 农田
                    2: '林地',      # 森林
                    3: '草地',      # 草地
                    4: '灌木地',    # 灌木
                    5: '湿地',      # 湿地
                    6: '水域',      # 水体
                    7: '苔原',      # 苔原
                    8: '建设用地',  # 人工表面/建设用地
                    9: '裸地',      # 裸地
                    10: '冰雪'      # 冰雪
                }

                # 根据实际数据分布创建地图数据
                results = []
                total_pixels = len(valid_data)

                # 计算每种土地利用类型的比例和区域
                for i, (value, count) in enumerate(zip(unique_values, counts)):
                    percentage = count / total_pixels

                    # 只处理占比超过1%的类型
                    if percentage < 0.01:
                        continue

                    landuse_type = clcd_mapping.get(int(value), f'其他类型({value})')
                    print(f"土地利用类型 {landuse_type}: {percentage*100:.1f}%")

                    # 根据比例创建区域边界（简化为条带状分布）
                    region_width = (maxx - minx) * percentage
                    region_start = minx + sum(counts[:i]) / total_pixels * (maxx - minx)
                    region_end = region_start + region_width

                    coordinates = [
                        [miny, region_start],  # 左下
                        [maxy, region_start],  # 左上
                        [maxy, region_end],    # 右上
                        [miny, region_end],    # 右下
                        [miny, region_start]   # 闭合
                    ]

                    # 获取对应的颜色
                    color = landuse_colors.get(landuse_type, landuse_colors.get('其他', '#DDA0DD'))
                    area_km2 = calculate_polygon_area(coordinates)

                    results.append({
                        'landuse_type': landuse_type,
                        'coordinates': coordinates,
                        'color': color,
                        'area_km2': round(area_km2, 2),
                        'description': f'{landuse_type}区域 ({percentage*100:.1f}%)',
                        'district': layer.province or '未知',
                        'source': 'real_raster_analysis',
                        'percentage': round(percentage * 100, 1)
                    })

                print(f"生成了 {len(results)} 个土地利用区域")
                return results

        except ImportError:
            print("Rasterio不可用，使用简化处理")
        except Exception as e:
            print(f"栅格数据读取失败: {e}")
            import traceback
            traceback.print_exc()

        except ImportError:
            print("Rasterio不可用，使用简化处理")
        except Exception as e:
            print(f"栅格数据读取失败: {e}")

        # 回退到简化处理
        bounds = layer.get_bounds_list()
        if not bounds or len(bounds) != 4:
            return []

        minx, miny, maxx, maxy = bounds

        # 创建基于栅格边界的简单多边形
        coordinates = [
            [miny, minx],  # 左下
            [maxy, minx],  # 左上
            [maxy, maxx],  # 右上
            [miny, maxx],  # 右下
            [miny, minx]   # 闭合
        ]

        # 根据文件名推断土地利用类型
        landuse_type = determine_landuse_type_from_filename(layer.name)
        color = landuse_colors.get(landuse_type, landuse_colors.get('其他', '#DDA0DD'))

        # 计算面积
        area_km2 = calculate_polygon_area(coordinates)

        return [{
            'landuse_type': landuse_type,
            'coordinates': coordinates,
            'color': color,
            'area_km2': round(area_km2, 2),
            'description': f'{landuse_type}区域（栅格数据）',
            'district': layer.province or '未知',
            'source': 'uploaded_raster_data'
        }]

    except Exception as e:
        print(f"处理栅格土地利用数据失败: {e}")
        return []


def process_vector_landuse_to_geojson(layer):
    """将矢量土地利用数据转换为GeoJSON格式"""
    try:
        # 检查是否有GIS库
        try:
            import geopandas as gpd
        except ImportError:
            print("GeoPandas不可用，无法处理矢量数据")
            return []

        # 读取Shapefile
        gdf = gpd.read_file(layer.file_path)

        # 确保使用WGS84坐标系
        try:
            if gdf.crs is None or str(gdf.crs) == 'EPSG:7' or 'EPSG:7' in str(gdf.crs):
                print(f"警告: 检测到无效坐标系 {gdf.crs}，假设为 EPSG:4326")
                gdf.crs = 'EPSG:4326'
            elif gdf.crs != 'EPSG:4326':
                gdf = gdf.to_crs('EPSG:4326')
        except Exception as e:
            print(f"坐标系转换失败: {e}，假设为 EPSG:4326")
            gdf.crs = 'EPSG:4326'

        # 土地利用类型颜色映射
        landuse_colors = get_landuse_color_mapping()

        features = []

        for idx, row in gdf.iterrows():
            try:
                # 获取几何对象
                geom = row.geometry

                # 确定土地利用类型
                landuse_type = determine_landuse_type(row, layer)

                # 获取颜色和透明度
                color = landuse_colors.get(landuse_type, landuse_colors.get('其他', '#DDA0DD'))
                opacity = get_landuse_opacity(landuse_type)

                # 计算面积（粗略估算）
                area_km2 = 0
                try:
                    # 转换到等面积投影计算面积
                    geom_area = gpd.GeoSeries([geom], crs='EPSG:4326').to_crs('EPSG:3857').area.iloc[0]
                    area_km2 = round(geom_area / 1000000, 2)  # 转换为平方公里
                except:
                    area_km2 = 0

                # 构建GeoJSON Feature
                feature = {
                    'type': 'Feature',
                    'geometry': geom.__geo_interface__,
                    'properties': {
                        'name': f'{landuse_type}区域_{idx}',
                        'landuse_type': landuse_type,
                        'fill_color': color,
                        'fill_opacity': opacity,
                        'area_km2': area_km2,
                        'description': f'{landuse_type}区域',
                        'province': layer.province or '未知',
                        'source': 'uploaded_vector_data',
                        'layer_name': layer.name
                    }
                }

                features.append(feature)

            except Exception as e:
                print(f"处理要素 {idx} 失败: {e}")
                continue

        return features

    except Exception as e:
        print(f"处理矢量土地利用数据失败: {e}")
        return []


def process_raster_landuse_to_geojson(layer):
    """将栅格土地利用数据转换为GeoJSON格式（简化版本）"""
    try:
        bounds = layer.get_bounds_list()
        if not bounds or len(bounds) != 4:
            return []

        minx, miny, maxx, maxy = bounds

        # 根据文件名推断土地利用类型
        landuse_type = determine_landuse_type_from_filename(layer.name)
        landuse_colors = get_landuse_color_mapping()
        color = landuse_colors.get(landuse_type, landuse_colors.get('其他', '#DDA0DD'))
        opacity = get_landuse_opacity(landuse_type)

        # 创建基于栅格边界的多边形几何
        geometry = {
            'type': 'Polygon',
            'coordinates': [[
                [minx, miny],  # 左下
                [minx, maxy],  # 左上
                [maxx, maxy],  # 右上
                [maxx, miny],  # 右下
                [minx, miny]   # 闭合
            ]]
        }

        # 计算面积
        area_km2 = abs((maxx - minx) * (maxy - miny)) * 111 * 111  # 粗略估算

        feature = {
            'type': 'Feature',
            'geometry': geometry,
            'properties': {
                'name': f'{landuse_type}区域（栅格）',
                'landuse_type': landuse_type,
                'fill_color': color,
                'fill_opacity': opacity,
                'area_km2': round(area_km2, 2),
                'description': f'{landuse_type}区域（栅格数据）',
                'province': layer.province or '未知',
                'source': 'uploaded_raster_data',
                'layer_name': layer.name
            }
        }

        return [feature]

    except Exception as e:
        print(f"处理栅格土地利用数据失败: {e}")
        return []


def get_landuse_color_mapping():
    """获取土地利用类型颜色映射"""
    return {
        '耕地': '#FFD700',      # 金色
        '林地': '#228B22',      # 森林绿
        '草地': '#90EE90',      # 浅绿色
        '水域': '#4169E1',      # 皇家蓝
        '建设用地': '#808080',   # 灰色
        '未利用地': '#D2B48C',   # 棕褐色
        '住宅用地': '#FFB6C1',   # 浅粉色
        '商业用地': '#FF6347',   # 番茄红
        '工业用地': '#8B4513',   # 马鞍棕
        '交通用地': '#696969',   # 暗灰色
        '其他': '#DDA0DD'       # 梅花色
    }


def get_landuse_opacity(landuse_type):
    """获取土地利用类型对应的透明度"""
    opacity_mapping = {
        '耕地': 0.7,
        '林地': 0.8,
        '草地': 0.6,
        '水域': 0.8,
        '建设用地': 0.7,
        '未利用地': 0.5,
        '住宅用地': 0.7,
        '商业用地': 0.7,
        '工业用地': 0.8,
        '交通用地': 0.6,
        '其他': 0.5
    }
    return opacity_mapping.get(landuse_type, 0.6)


def determine_landuse_type(row, layer):
    """根据数据行和图层信息确定土地利用类型"""
    # 尝试从不同的字段获取土地利用类型
    possible_fields = ['landuse', 'land_use', 'type', 'class', 'category', 'LANDUSE', 'LAND_USE', 'TYPE', 'CLASS']

    for field in possible_fields:
        if hasattr(row, field) and row[field] is not None:
            landuse_value = str(row[field]).strip()
            if landuse_value:
                return normalize_landuse_type(landuse_value)

    # 如果没有找到明确的土地利用类型字段，尝试从文件名推断
    return determine_landuse_type_from_filename(layer.name)


def normalize_landuse_type(landuse_value):
    """标准化土地利用类型名称"""
    landuse_value = str(landuse_value).strip().lower()

    # 土地利用类型映射表
    landuse_mapping = {
        # 耕地相关
        'farmland': '耕地',
        'cropland': '耕地',
        'agricultural': '耕地',
        'cultivated': '耕地',
        '农田': '耕地',
        '农地': '耕地',
        '种植': '耕地',

        # 林地相关
        'forest': '林地',
        'woodland': '林地',
        'trees': '林地',
        '森林': '林地',
        '树林': '林地',
        '林业': '林地',

        # 草地相关
        'grassland': '草地',
        'pasture': '草地',
        'meadow': '草地',
        '草原': '草地',
        '牧场': '草地',
        '草坪': '草地',

        # 水域相关
        'water': '水域',
        'river': '水域',
        'lake': '水域',
        'pond': '水域',
        '水体': '水域',
        '河流': '水域',
        '湖泊': '水域',
        '池塘': '水域',

        # 建设用地相关
        'urban': '建设用地',
        'built': '建设用地',
        'construction': '建设用地',
        'developed': '建设用地',
        '城市': '建设用地',
        '建筑': '建设用地',
        '开发': '建设用地',

        # 住宅用地
        'residential': '住宅用地',
        '住宅': '住宅用地',
        '居住': '住宅用地',

        # 商业用地
        'commercial': '商业用地',
        '商业': '商业用地',
        '商务': '商业用地',

        # 工业用地
        'industrial': '工业用地',
        '工业': '工业用地',
        '制造': '工业用地',

        # 交通用地
        'transport': '交通用地',
        'transportation': '交通用地',
        'road': '交通用地',
        '交通': '交通用地',
        '道路': '交通用地',

        # 未利用地
        'unused': '未利用地',
        'barren': '未利用地',
        'vacant': '未利用地',
        '荒地': '未利用地',
        '空地': '未利用地',
        '未利用': '未利用地'
    }

    # 查找匹配的类型
    for key, value in landuse_mapping.items():
        if key in landuse_value:
            return value

    # 如果没有找到匹配，返回原值或默认值
    return landuse_value if landuse_value else '其他'


def determine_landuse_type(row, layer):
    """根据属性确定土地利用类型"""
    # 常见的土地利用类型字段名
    landuse_fields = ['landuse', 'land_use', 'type', 'class', 'category', '类型', '用地类型', '土地利用']

    for field in landuse_fields:
        if field in row.index and pd.notna(row[field]):
            value = str(row[field]).strip()
            return map_landuse_value(value)

    # 如果没有找到合适的字段，根据文件名推断
    return determine_landuse_type_from_filename(layer.name)


def determine_landuse_type_from_filename(filename):
    """根据文件名推断土地利用类型"""
    filename_lower = filename.lower()

    if any(keyword in filename_lower for keyword in ['耕地', 'farmland', 'agriculture', 'crop']):
        return '耕地'
    elif any(keyword in filename_lower for keyword in ['林地', 'forest', 'wood', 'tree']):
        return '林地'
    elif any(keyword in filename_lower for keyword in ['草地', 'grass', 'pasture']):
        return '草地'
    elif any(keyword in filename_lower for keyword in ['水域', 'water', 'river', 'lake']):
        return '水域'
    elif any(keyword in filename_lower for keyword in ['建设', 'urban', 'built', 'construction']):
        return '建设用地'
    elif any(keyword in filename_lower for keyword in ['住宅', 'residential']):
        return '住宅用地'
    elif any(keyword in filename_lower for keyword in ['商业', 'commercial']):
        return '商业用地'
    elif any(keyword in filename_lower for keyword in ['工业', 'industrial']):
        return '工业用地'
    else:
        return '其他'


def map_landuse_value(value):
    """映射土地利用值到标准类型"""
    value_lower = value.lower()

    # 数字编码映射
    if value.isdigit():
        code = int(value)
        if code == 1:
            return '耕地'
        elif code == 2:
            return '林地'
        elif code == 3:
            return '草地'
        elif code == 4:
            return '水域'
        elif code == 5:
            return '建设用地'
        elif code == 6:
            return '未利用地'

    # 文本映射
    if any(keyword in value_lower for keyword in ['耕地', 'farmland', 'agriculture', 'crop']):
        return '耕地'
    elif any(keyword in value_lower for keyword in ['林地', 'forest', 'wood', 'tree']):
        return '林地'
    elif any(keyword in value_lower for keyword in ['草地', 'grass', 'pasture']):
        return '草地'
    elif any(keyword in value_lower for keyword in ['水域', 'water', 'river', 'lake']):
        return '水域'
    elif any(keyword in value_lower for keyword in ['建设', 'urban', 'built', 'construction']):
        return '建设用地'
    elif any(keyword in value_lower for keyword in ['住宅', 'residential']):
        return '住宅用地'
    elif any(keyword in value_lower for keyword in ['商业', 'commercial']):
        return '商业用地'
    elif any(keyword in value_lower for keyword in ['工业', 'industrial']):
        return '工业用地'
    else:
        return value  # 保持原值


def process_vector_administrative_data(layer):
    """处理矢量行政区划数据"""
    try:
        # 检查是否有GIS库
        try:
            import geopandas as gpd
        except ImportError:
            print("GeoPandas不可用，无法处理矢量数据")
            return []

        # 读取Shapefile
        gdf = gpd.read_file(layer.file_path)

        # 确保使用WGS84坐标系
        try:
            # 检查坐标系是否有效
            if gdf.crs is None or str(gdf.crs) == 'EPSG:7' or 'EPSG:7' in str(gdf.crs):
                print(f"警告: 检测到无效坐标系 {gdf.crs}，假设为 EPSG:4326")
                gdf.crs = 'EPSG:4326'
            elif gdf.crs != 'EPSG:4326':
                gdf = gdf.to_crs('EPSG:4326')
        except Exception as e:
            print(f"坐标系转换失败: {e}，假设为 EPSG:4326")
            gdf.crs = 'EPSG:4326'

        admin_data = []

        for idx, row in gdf.iterrows():
            try:
                # 获取几何对象
                geom = row.geometry

                # 确定行政区名称
                admin_name = determine_admin_name(row, layer)

                # 转换几何为坐标列表
                coordinates = []
                if geom.geom_type == 'Polygon':
                    # 获取外环坐标
                    coords = list(geom.exterior.coords)
                    coordinates = [[lat, lng] for lng, lat in coords]
                elif geom.geom_type == 'MultiPolygon':
                    # 处理多边形，取第一个
                    if len(geom.geoms) > 0:
                        coords = list(geom.geoms[0].exterior.coords)
                        coordinates = [[lat, lng] for lng, lat in coords]

                if coordinates:
                    # 计算面积（粗略估算）
                    area_km2 = calculate_polygon_area(coordinates)

                    admin_data.append({
                        'name': admin_name,
                        'coordinates': coordinates,
                        'area_km2': round(area_km2, 2),
                        'population': estimate_population(area_km2),
                        'flood_risk_level': '中等',  # 默认风险等级
                        'district': layer.province or '未知',
                        'source': 'uploaded_data'
                    })

            except Exception as e:
                print(f"处理行政区划要素 {idx} 失败: {e}")
                continue

        return admin_data

    except Exception as e:
        print(f"处理矢量行政区划数据失败: {e}")
        return []

def determine_admin_name(row, layer):
    """根据属性确定行政区名称"""
    # 常见的行政区名称字段
    name_fields = ['name', 'NAME', 'admin_name', 'district', 'county', '名称', '区县', '行政区']

    for field in name_fields:
        if field in row.index and pd.notna(row[field]):
            return str(row[field]).strip()

    # 如果没有找到合适的字段，使用图层名称
    return layer.name or '未知区域'

def estimate_population(area_km2):
    """根据面积估算人口（简化版本）"""
    # 这是一个非常简化的估算，实际应用中应该使用真实的人口数据
    # 假设平均人口密度为每平方公里500人
    return int(area_km2 * 500)

def calculate_polygon_area(coordinates):
    """计算多边形面积（平方公里）"""
    try:
        # 使用鞋带公式计算面积（简化版本）
        if len(coordinates) < 3:
            return 0

        # 转换为数值
        coords = [(float(coord[0]), float(coord[1])) for coord in coordinates]

        # 鞋带公式
        area = 0
        n = len(coords)
        for i in range(n):
            j = (i + 1) % n
            area += coords[i][0] * coords[j][1]
            area -= coords[j][0] * coords[i][1]

        area = abs(area) / 2.0

        # 粗略转换为平方公里（这是一个近似值）
        # 1度约等于111公里
        area_km2 = area * (111 * 111)

        return area_km2

    except Exception as e:
        print(f"计算面积失败: {e}")
        return 0


def create_empty_tile():
    """创建透明的空瓦片"""
    from flask import Response
    import base64

    # 1x1 透明PNG的base64编码
    transparent_png = base64.b64decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==')

    return Response(
        transparent_png,
        mimetype='image/png',
        headers={
            'Cache-Control': 'public, max-age=3600'
        }
    )

def generate_risk_distribution_data(city):
    """生成基于行政区划的风险分布数据"""
    # 获取城市的行政区划信息
    districts_data = generate_administrative_data(city)
    risk_zones = []
    
    risk_colors = {'低': '#28a745', '中': '#ffc107', '高': '#dc3545'}
    
    for district in districts_data:
        district_name = district['name']
        center_lat, center_lng = district['center']
        risk_level = district['flood_risk_level']
        risk_score = district['risk_score']
        
        # 基于区域风险等级生成多个风险点
        num_points = {'低': 3, '中': 5, '高': 8}[risk_level]
        area_factor = (district['area_km2'] / 100) ** 0.5 * 0.03
        
        for i in range(num_points):
            # 在区域范围内生成风险点
            offset_lat = (random.random() - 0.5) * area_factor
            offset_lng = (random.random() - 0.5) * area_factor
            
            risk_zones.append({
                'id': f'{district_name}_risk_{i}',
                'lat': center_lat + offset_lat,
                'lng': center_lng + offset_lng,
                'risk_level': risk_level,
                'risk_score': risk_score + random.uniform(-0.1, 0.1),
                'color': risk_colors[risk_level],
                'radius': random.uniform(300, 1000) * (risk_score + 0.2),
                'district': district_name,
                'description': f'{district_name}{risk_level}风险区域',
                'factors': get_risk_factors(risk_level, district_name)
            })
    
    return risk_zones


def generate_administrative_data(city):
    """生成行政区划数据"""
    # 详细的行政区划配置，包含每个区的具体坐标和特征
    city_boundaries = {
        'zhengzhou': {
            'districts': {
                '中原区': {'center': [34.7447, 113.6131], 'area': 97.1, 'population': 97.5, 'risk_level': 'medium'},
                '二七区': {'center': [34.7227, 113.6394], 'area': 159.2, 'population': 88.2, 'risk_level': 'medium'},
                '管城区': {'center': [34.7542, 113.6772], 'area': 204.3, 'population': 73.6, 'risk_level': 'high'},
                '金水区': {'center': [34.8022, 113.6602], 'area': 242.7, 'population': 142.3, 'risk_level': 'low'},
                '上街区': {'center': [34.8024, 113.3074], 'area': 64.7, 'population': 13.8, 'risk_level': 'low'},
                '惠济区': {'center': [34.8670, 113.6167], 'area': 206.5, 'population': 36.2, 'risk_level': 'medium'}
            },
            'center': {'lat': 34.7667, 'lng': 113.65}
        },
        'xian': {
            'districts': {
                '新城区': {'center': [34.2658, 108.9606], 'area': 31.2, 'population': 57.1, 'risk_level': 'medium'},
                '碑林区': {'center': [34.2301, 108.9439], 'area': 22.0, 'population': 80.5, 'risk_level': 'high'},
                '莲湖区': {'center': [34.2669, 108.9368], 'area': 38.3, 'population': 71.2, 'risk_level': 'medium'},
                '灞桥区': {'center': [34.2740, 109.0633], 'area': 332.0, 'population': 65.4, 'risk_level': 'low'},
                '未央区': {'center': [34.2978, 108.9469], 'area': 262.1, 'population': 108.7, 'risk_level': 'medium'},
                '雁塔区': {'center': [34.2120, 108.9280], 'area': 152.0, 'population': 141.2, 'risk_level': 'high'},
                '阎良区': {'center': [34.6622, 109.2267], 'area': 244.4, 'population': 27.8, 'risk_level': 'low'},
                '临潼区': {'center': [34.3720, 109.2140], 'area': 915.9, 'population': 67.4, 'risk_level': 'medium'},
                '长安区': {'center': [34.1594, 108.9061], 'area': 1580.0, 'population': 114.5, 'risk_level': 'low'}
            },
            'center': {'lat': 34.2667, 'lng': 108.95}
        },
        'xiamen': {
            'districts': {
                '思明区': {'center': [24.4454, 118.0820], 'area': 84.0, 'population': 108.9, 'risk_level': 'high'},
                '海沧区': {'center': [24.4843, 118.0325], 'area': 186.5, 'population': 48.7, 'risk_level': 'medium'},
                '湖里区': {'center': [24.5116, 118.1475], 'area': 65.8, 'population': 101.3, 'risk_level': 'high'},
                '集美区': {'center': [24.5759, 118.0975], 'area': 276.6, 'population': 91.2, 'risk_level': 'medium'},
                '同安区': {'center': [24.7233, 118.1475], 'area': 1329.6, 'population': 54.8, 'risk_level': 'low'},
                '翔安区': {'center': [24.6186, 118.2460], 'area': 351.9, 'population': 45.6, 'risk_level': 'low'}
            },
            'center': {'lat': 24.4667, 'lng': 118.10}
        }
    }
    
    city_data = city_boundaries.get(city, city_boundaries['zhengzhou'])
    districts = []
    
    for district_name, district_info in city_data['districts'].items():
        # 基于实际区域中心生成边界坐标
        center_lat, center_lng = district_info['center']
        district_boundary = []
        
        # 根据区域面积调整边界大小
        area_factor = (district_info['area'] / 100) ** 0.5 * 0.05  # 面积越大边界越大
        
        for angle in range(0, 360, 30):  # 更密集的边界点
            angle_rad = angle * 3.14159 / 180
            # 添加一些随机性来模拟真实的行政边界
            radius = area_factor * random.uniform(0.6, 1.4)
            lat = center_lat + radius * cos_approximation(angle_rad)
            lng = center_lng + radius * sin_approximation(angle_rad)
            district_boundary.append([lat, lng])
        
        districts.append({
            'name': district_name,
            'boundary': district_boundary,
            'center': district_info['center'],
            'population': district_info['population'] * 10000,  # 转换为实际人数
            'area_km2': district_info['area'],
            'flood_risk_level': get_risk_level_chinese(district_info['risk_level']),
            'risk_score': get_risk_score(district_info['risk_level'])
        })
    
    return districts


def get_risk_level_chinese(level):
    """转换风险等级为中文"""
    levels = {'low': '低', 'medium': '中', 'high': '高'}
    return levels.get(level, '中')


def get_risk_score(level):
    """获取风险评分"""
    scores = {'low': random.uniform(0.1, 0.3), 'medium': random.uniform(0.4, 0.7), 'high': random.uniform(0.7, 1.0)}
    return round(scores.get(level, 0.5), 2)


def get_real_administrative_data(city=None, province=None):
    """从数据库获取真实的行政区划数据"""
    from app.models import GISLayer

    try:
        # 构建查询条件
        query = GISLayer.query.filter(
            GISLayer.data_category == 'administrative',
            GISLayer.processing_status == 'processed'
        )

        # 根据省份或城市筛选
        if province:
            query = query.filter(GISLayer.province == province)
        elif city:
            # 如果没有省份参数，尝试通过城市名称匹配
            query = query.filter(
                db.or_(
                    GISLayer.province.like(f'%{city}%'),
                    GISLayer.name.like(f'%{city}%'),
                    GISLayer.display_name.like(f'%{city}%')
                )
            )

        layers = query.all()

        if not layers:
            return []

        admin_data = []

        for layer in layers:
            try:
                # 处理矢量数据
                if layer.layer_type == 'vector' and layer.file_path:
                    vector_data = process_vector_administrative_data(layer)
                    admin_data.extend(vector_data)

            except Exception as e:
                print(f"处理行政区划图层 {layer.name} 失败: {e}")
                continue

        return admin_data

    except Exception as e:
        print(f"获取行政区划数据失败: {e}")
        return []

def get_real_risk_data(city=None, province=None):
    """从数据库获取真实的风险数据"""
    try:
        from app.models import RiskAssessmentProject, RiskCalculationResult, RiskAssessmentData

        # 构建查询条件
        query = RiskAssessmentProject.query

        if province:
            query = query.filter(RiskAssessmentProject.location.like(f'%{province}%'))
        elif city:
            query = query.filter(RiskAssessmentProject.location.like(f'%{city}%'))

        projects = query.all()

        if not projects:
            return []

        risk_data = []
        for project in projects:
            # 获取项目的风险计算结果
            results = RiskCalculationResult.query.filter_by(project_id=project.id).all()

            for result in results:
                if result.latitude and result.longitude:
                    risk_data.append({
                        'id': f'risk_{result.id}',
                        'lat': float(result.latitude),
                        'lng': float(result.longitude),
                        'risk_level': result.risk_level or 'medium',
                        'risk_score': float(result.risk_index or 0.5),
                        'district': project.location,
                        'description': f'{project.location}风险区域',
                        'source': 'uploaded_data'
                    })

        return risk_data

    except Exception as e:
        print(f"获取风险数据失败: {e}")
        return []

def get_real_terrain_data(city=None, province=None):
    """从数据库获取真实的地形数据"""
    try:
        from app.models import GISLayer

        # 构建查询条件
        query = GISLayer.query.filter(
            GISLayer.data_category == 'elevation',
            GISLayer.processing_status == 'processed'
        )

        if province:
            query = query.filter(GISLayer.province == province)
        elif city:
            query = query.filter(
                db.or_(
                    GISLayer.province.like(f'%{city}%'),
                    GISLayer.name.like(f'%{city}%'),
                    GISLayer.display_name.like(f'%{city}%')
                )
            )

        layers = query.all()

        if not layers:
            return []

        terrain_data = []
        for layer in layers:
            try:
                # 处理地形数据
                if layer.layer_type == 'raster' and layer.file_path:
                    # 这里可以添加具体的地形数据处理逻辑
                    # 暂时返回基本信息
                    bounds = layer.get_bounds_list()
                    if bounds and len(bounds) == 4:
                        terrain_data.append({
                            'name': layer.display_name or layer.name,
                            'bounds': bounds,
                            'source': 'uploaded_data',
                            'layer_id': layer.id
                        })
            except Exception as e:
                print(f"处理地形图层 {layer.name} 失败: {e}")
                continue

        return terrain_data

    except Exception as e:
        print(f"获取地形数据失败: {e}")
        return []

def get_real_landuse_data(city=None, province=None):
    """从数据库获取真实的土地利用数据"""
    from app.models import GISLayer

    try:
        # 构建查询条件
        query = GISLayer.query.filter(
            GISLayer.data_category == 'landuse',
            GISLayer.processing_status == 'processed'
        )

        # 根据省份或城市筛选
        if province:
            query = query.filter(GISLayer.province == province)
        elif city:
            # 如果没有省份参数，尝试通过城市名称匹配
            query = query.filter(
                db.or_(
                    GISLayer.province.like(f'%{city}%'),
                    GISLayer.name.like(f'%{city}%'),
                    GISLayer.display_name.like(f'%{city}%')
                )
            )

        layers = query.all()

        if not layers:
            return []

        landuse_data = []

        # 土地利用类型颜色映射
        landuse_colors = {
            '耕地': '#FFD700',      # 金色
            '林地': '#228B22',      # 森林绿
            '草地': '#90EE90',      # 浅绿色
            '水域': '#4169E1',      # 皇家蓝
            '建设用地': '#808080',   # 灰色
            '未利用地': '#D2B48C',   # 棕褐色
            '住宅用地': '#FFB6C1',   # 浅粉色
            '商业用地': '#FF6347',   # 番茄红
            '工业用地': '#8B4513',   # 马鞍棕
            '交通用地': '#696969',   # 暗灰色
            '其他': '#DDA0DD'       # 梅花色
        }

        for layer in layers:
            try:
                # 处理矢量数据
                if layer.layer_type == 'vector' and layer.file_path:
                    vector_data = process_vector_landuse_data(layer, landuse_colors)
                    landuse_data.extend(vector_data)

                # 处理栅格数据
                elif layer.layer_type == 'raster' and layer.file_path:
                    raster_data = process_raster_landuse_data(layer, landuse_colors)
                    landuse_data.extend(raster_data)

            except Exception as e:
                print(f"处理图层 {layer.name} 失败: {e}")
                continue

        return landuse_data

    except Exception as e:
        print(f"获取土地利用数据失败: {e}")
        return []




def cos_approximation(x):
    """余弦函数近似"""
    # 简单的余弦近似，适用于小角度
    return 1 - (x * x) / 2 + (x * x * x * x) / 24


def sin_approximation(x):
    """正弦函数近似"""
    # 简单的正弦近似，适用于小角度
    return x - (x * x * x) / 6 + (x * x * x * x * x) / 120



# ==================== 基于真实数据库数据的图表数据生成函数 ====================

def generate_real_rainfall_chart_data(province, city, year=None):
    """基于真实数据库数据生成降雨量图表数据"""
    from app.models import RiskAssessmentProject, RiskAssessmentData

    try:
        # 构建查询条件
        query = db.session.query(RiskAssessmentData, RiskAssessmentProject.assessment_year).join(
            RiskAssessmentProject, RiskAssessmentData.project_id == RiskAssessmentProject.id
        ).filter(
            RiskAssessmentProject.location == province
        )

        if city:
            query = query.filter(RiskAssessmentData.location_name.like(f'%{city}%'))

        # 如果指定了年份，只返回该年份的数据
        if year:
            query = query.filter(RiskAssessmentProject.assessment_year == year)

            # 获取数据
            data_records = query.all()

            if not data_records:
                return None

            # 按地区组织数据（单年份）
            regions = []
            rainfall_data = []

            for record, _ in data_records:
                if record.annual_precipitation is not None:
                    regions.append(record.location_name or record.data_name or f'区域{record.id}')
                    rainfall_data.append(round(record.annual_precipitation, 1))

            if not rainfall_data:
                return None

            return {
                'regions': regions,
                'rainfall': rainfall_data,
                'unit': 'mm',
                'total': round(sum(rainfall_data), 1),
                'average': round(sum(rainfall_data) / len(rainfall_data), 1),
                'max': max(rainfall_data),
                'min': min(rainfall_data),
                'count': len(rainfall_data)
            }
        else:
            # 如果没有指定年份，返回多年数据
            data_records = query.all()

            if not data_records:
                return None

            # 按年份组织数据
            yearly_data = {}
            for record, assessment_year in data_records:
                if record.annual_precipitation is not None:
                    if assessment_year not in yearly_data:
                        yearly_data[assessment_year] = []
                    yearly_data[assessment_year].append(record.annual_precipitation)

            if not yearly_data:
                return None

            # 计算每年的平均值
            years = sorted(yearly_data.keys())
            rainfall_values = []

            for year in years:
                avg_rainfall = sum(yearly_data[year]) / len(yearly_data[year])
                rainfall_values.append(round(avg_rainfall, 1))

            return {
                'years': years,
                'rainfall': rainfall_values,
                'unit': 'mm',
                'total': round(sum(rainfall_values), 1),
                'average': round(sum(rainfall_values) / len(rainfall_values), 1),
                'max': max(rainfall_values),
                'min': min(rainfall_values),
                'count': len(rainfall_values)
            }

    except Exception as e:
        print(f"生成真实降雨量数据失败: {e}")
        return None


def generate_real_population_density_chart_data(province, city, year=None):
    """基于真实数据库数据生成人口密度图表数据"""
    from app.models import RiskAssessmentProject, RiskAssessmentData

    try:
        # 构建查询条件
        query = db.session.query(RiskAssessmentData, RiskAssessmentProject.assessment_year).join(
            RiskAssessmentProject, RiskAssessmentData.project_id == RiskAssessmentProject.id
        ).filter(
            RiskAssessmentProject.location == province
        )

        if city:
            query = query.filter(RiskAssessmentData.location_name.like(f'%{city}%'))

        # 如果指定了年份，只返回该年份的数据
        if year:
            query = query.filter(RiskAssessmentProject.assessment_year == year)

            # 获取数据
            data_records = query.all()

            if not data_records:
                return None

            # 按地区组织数据（单年份）
            regions = []
            density_data = []

            for record, _ in data_records:
                if record.population_density is not None:
                    regions.append(record.location_name or record.data_name or f'区域{record.id}')
                    density_data.append(round(record.population_density, 1))

            if not density_data:
                return None

            return {
                'regions': regions,
                'density': density_data,
                'unit': '人/km²',
                'total': round(sum(density_data), 1),
                'average': round(sum(density_data) / len(density_data), 1),
                'max': max(density_data),
                'min': min(density_data),
                'count': len(density_data)
            }
        else:
            # 如果没有指定年份，返回多年数据
            data_records = query.all()

            if not data_records:
                return None

            # 按年份组织数据
            yearly_data = {}
            for record, assessment_year in data_records:
                if record.population_density is not None:
                    if assessment_year not in yearly_data:
                        yearly_data[assessment_year] = []
                    yearly_data[assessment_year].append(record.population_density)

            if not yearly_data:
                return None

            # 计算每年的平均值
            years = sorted(yearly_data.keys())
            density_values = []

            for year in years:
                avg_density = sum(yearly_data[year]) / len(yearly_data[year])
                density_values.append(round(avg_density, 1))

            return {
                'years': years,
                'density': density_values,
                'unit': '人/km²',
                'total': round(sum(density_values), 1),
                'average': round(sum(density_values) / len(density_values), 1),
                'max': max(density_values),
                'min': min(density_values),
                'count': len(density_values)
            }

    except Exception as e:
        print(f"生成真实人口密度数据失败: {e}")
        return None


def generate_real_drainage_density_chart_data(province, city, year=None):
    """基于真实数据库数据生成排水管密度图表数据"""
    from app.models import RiskAssessmentProject, RiskAssessmentData

    try:
        # 构建查询条件
        query = db.session.query(RiskAssessmentData, RiskAssessmentProject.assessment_year).join(
            RiskAssessmentProject, RiskAssessmentData.project_id == RiskAssessmentProject.id
        ).filter(
            RiskAssessmentProject.location == province
        )

        if city:
            query = query.filter(RiskAssessmentData.location_name.like(f'%{city}%'))

        # 如果指定了年份，只返回该年份的数据
        if year:
            query = query.filter(RiskAssessmentProject.assessment_year == year)

            # 获取数据
            data_records = query.all()

            if not data_records:
                return None

            # 按地区组织数据（单年份）
            regions = []
            density_data = []

            for record, _ in data_records:
                if record.drainage_network_density is not None:
                    regions.append(record.location_name or record.data_name or f'区域{record.id}')
                    density_data.append(round(record.drainage_network_density, 2))

            if not density_data:
                return None

            return {
                'regions': regions,
                'density': density_data,
                'unit': 'km/km²',
                'total': round(sum(density_data), 2),
                'average': round(sum(density_data) / len(density_data), 2),
                'max': max(density_data),
                'min': min(density_data),
                'count': len(density_data)
            }
        else:
            # 如果没有指定年份，返回多年数据
            data_records = query.all()

            if not data_records:
                return None

            # 按年份组织数据
            yearly_data = {}
            for record, assessment_year in data_records:
                if record.drainage_network_density is not None:
                    if assessment_year not in yearly_data:
                        yearly_data[assessment_year] = []
                    yearly_data[assessment_year].append(record.drainage_network_density)

            if not yearly_data:
                return None

            # 计算每年的平均值
            years = sorted(yearly_data.keys())
            density_values = []

            for year in years:
                avg_density = sum(yearly_data[year]) / len(yearly_data[year])
                density_values.append(round(avg_density, 2))

            return {
                'years': years,
                'density': density_values,
                'unit': 'km/km²',
                'total': round(sum(density_values), 2),
                'average': round(sum(density_values) / len(density_values), 2),
                'max': max(density_values),
                'min': min(density_values),
                'count': len(density_values)
            }

    except Exception as e:
        print(f"生成真实排水管密度数据失败: {e}")
        return None


def generate_real_gdp_density_chart_data(province, city, year=None):
    """基于真实数据库数据生成GDP密度图表数据"""
    from app.models import RiskAssessmentProject, RiskAssessmentData

    try:
        # 构建查询条件
        query = db.session.query(RiskAssessmentData, RiskAssessmentProject.assessment_year).join(
            RiskAssessmentProject, RiskAssessmentData.project_id == RiskAssessmentProject.id
        ).filter(
            RiskAssessmentProject.location == province
        )

        if city:
            query = query.filter(RiskAssessmentData.location_name.like(f'%{city}%'))

        # 如果指定了年份，只返回该年份的数据
        if year:
            query = query.filter(RiskAssessmentProject.assessment_year == year)

            # 获取数据
            data_records = query.all()

            if not data_records:
                return None

            # 按地区组织数据（单年份）
            regions = []
            gdp_data = []

            for record, _ in data_records:
                if record.gdp_per_area is not None:
                    regions.append(record.location_name or record.data_name or f'区域{record.id}')
                    gdp_data.append(round(record.gdp_per_area, 1))

            if not gdp_data:
                return None

            return {
                'regions': regions,
                'gdp_density': gdp_data,
                'unit': '万元/km²',
                'total': round(sum(gdp_data), 1),
                'average': round(sum(gdp_data) / len(gdp_data), 1),
                'max': max(gdp_data),
                'min': min(gdp_data),
                'count': len(gdp_data)
            }
        else:
            # 如果没有指定年份，返回多年数据
            data_records = query.all()

            if not data_records:
                return None

            # 按年份组织数据
            yearly_data = {}
            for record, assessment_year in data_records:
                if record.gdp_per_area is not None:
                    if assessment_year not in yearly_data:
                        yearly_data[assessment_year] = []
                    yearly_data[assessment_year].append(record.gdp_per_area)

            if not yearly_data:
                return None

            # 计算每年的平均值
            years = sorted(yearly_data.keys())
            gdp_values = []

            for year in years:
                avg_gdp = sum(yearly_data[year]) / len(yearly_data[year])
                gdp_values.append(round(avg_gdp, 1))

            return {
                'years': years,
                'gdp_density': gdp_values,
                'unit': '万元/km²',
                'total': round(sum(gdp_values), 1),
                'average': round(sum(gdp_values) / len(gdp_values), 1),
                'max': max(gdp_values),
                'min': min(gdp_values),
                'count': len(gdp_values)
            }

    except Exception as e:
        print(f"生成真实GDP密度数据失败: {e}")
        return None


def generate_real_elevation_chart_data(province, city, year):
    """基于真实数据库数据生成高程图表数据"""
    from app.models import RiskAssessmentProject, RiskAssessmentData

    try:
        # 构建查询条件
        query = db.session.query(RiskAssessmentData).join(
            RiskAssessmentProject, RiskAssessmentData.project_id == RiskAssessmentProject.id
        ).filter(
            RiskAssessmentProject.location == province
        )

        if year:
            query = query.filter(RiskAssessmentProject.assessment_year == year)

        if city:
            query = query.filter(RiskAssessmentData.location_name.like(f'%{city}%'))

        # 获取数据
        data_records = query.all()

        if not data_records:
            return None

        # 按地区组织数据
        regions = []
        elevation_data = []

        for record in data_records:
            if record.elevation is not None:
                regions.append(record.location_name or record.data_name or f'区域{record.id}')
                elevation_data.append(round(record.elevation, 1))

        if not elevation_data:
            return None

        return {
            'regions': regions,
            'elevation': elevation_data,
            'unit': 'm',
            'average': round(sum(elevation_data) / len(elevation_data), 1),
            'max': max(elevation_data),
            'min': min(elevation_data),
            'range': round(max(elevation_data) - min(elevation_data), 1),
            'count': len(elevation_data)
        }

    except Exception as e:
        print(f"生成真实高程数据失败: {e}")
        return None


def generate_real_average_elevation_chart_data(province, city):
    """基于真实数据库数据生成平均高程图表数据"""
    from app.models import RiskAssessmentProject, RiskAssessmentData

    try:
        # 构建查询条件
        query = db.session.query(RiskAssessmentData, RiskAssessmentProject.assessment_year).join(
            RiskAssessmentProject, RiskAssessmentData.project_id == RiskAssessmentProject.id
        ).filter(
            RiskAssessmentProject.location == province
        )

        if city:
            query = query.filter(RiskAssessmentData.location_name.like(f'%{city}%'))

        # 获取所有数据
        data_records = query.all()

        if not data_records:
            return None

        # 按年份组织数据
        yearly_data = {}
        for record, assessment_year in data_records:
            if record.elevation is not None:
                if assessment_year not in yearly_data:
                    yearly_data[assessment_year] = []
                yearly_data[assessment_year].append(record.elevation)

        if not yearly_data:
            return None

        # 计算每年的平均值
        years = sorted(yearly_data.keys())
        elevation_values = []

        for year in years:
            avg_elevation = sum(yearly_data[year]) / len(yearly_data[year])
            elevation_values.append(round(avg_elevation, 1))

        return {
            'years': years,
            'elevation': elevation_values,
            'unit': 'm',
            'total': round(sum(elevation_values), 1),
            'average': round(sum(elevation_values) / len(elevation_values), 1),
            'max': max(elevation_values),
            'min': min(elevation_values),
            'count': len(elevation_values)
        }

    except Exception as e:
        print(f"生成真实平均高程数据失败: {e}")
        return None


@bp.route('/api/gis-layer-tiles-v2/<int:layer_id>/<int:z>/<int:x>/<int:y>.png')
def get_gis_layer_tile_v2(layer_id, z, x, y):
    """获取GIS图层的瓦片数据"""
    try:
        if not GIS_LIBS_AVAILABLE:
            return "GIS库不可用", 503

        # 获取图层信息
        layer = GISLayer.query.get_or_404(layer_id)

        if layer.layer_type != 'raster':
            return "只支持栅格图层", 400

        if not os.path.exists(layer.file_path):
            return "图层文件不存在", 404

        # 生成瓦片
        tile_data = generate_raster_tile(layer.file_path, z, x, y)

        if tile_data is None:
            return "无法生成瓦片", 500

        # 返回PNG图片
        from flask import Response
        return Response(tile_data, mimetype='image/png')

    except Exception as e:
        print(f"获取瓦片失败: {e}")
        return "服务器错误", 500


def generate_raster_tile(raster_path, z, x, y, tile_size=256):
    """生成栅格瓦片"""
    try:
        if not GIS_LIBS_AVAILABLE:
            return None

        # 计算瓦片边界
        tile_bounds = mercantile.bounds(x, y, z)

        with rasterio.open(raster_path) as src:
            # 将瓦片边界转换到数据的坐标系
            if src.crs != CRS.from_epsg(4326):
                tile_bounds_transformed = transform_bounds(
                    CRS.from_epsg(4326), src.crs,
                    tile_bounds.west, tile_bounds.south,
                    tile_bounds.east, tile_bounds.north
                )
            else:
                tile_bounds_transformed = tile_bounds

            # 计算窗口
            window = rasterio.windows.from_bounds(
                tile_bounds_transformed[0], tile_bounds_transformed[1],
                tile_bounds_transformed[2], tile_bounds_transformed[3],
                src.transform
            )

            # 读取数据
            data = src.read(1, window=window, out_shape=(tile_size, tile_size))

            if NUMPY_AVAILABLE:
                # 处理无数据值
                if src.nodata is not None:
                    data = np.where(data == src.nodata, np.nan, data)

                # 数据归一化到0-255
                if not np.all(np.isnan(data)):
                    data_min = np.nanmin(data)
                    data_max = np.nanmax(data)
                    if data_max > data_min:
                        data_normalized = ((data - data_min) / (data_max - data_min) * 255).astype(np.uint8)
                    else:
                        data_normalized = np.full_like(data, 128, dtype=np.uint8)
                else:
                    data_normalized = np.zeros((tile_size, tile_size), dtype=np.uint8)

                # 创建RGBA图像
                rgba_data = np.zeros((tile_size, tile_size, 4), dtype=np.uint8)

                # 应用颜色映射（简单的灰度映射）
                rgba_data[:, :, 0] = data_normalized  # R
                rgba_data[:, :, 1] = data_normalized  # G
                rgba_data[:, :, 2] = data_normalized  # B
                rgba_data[:, :, 3] = np.where(np.isnan(data), 0, 255)  # Alpha

                if PIL_AVAILABLE:
                    # 转换为PIL图像
                    image = Image.fromarray(rgba_data, 'RGBA')

                    # 保存为PNG字节流
                    img_buffer = io.BytesIO()
                    image.save(img_buffer, format='PNG')
                    img_buffer.seek(0)

                    return img_buffer.getvalue()

            # 如果numpy不可用，返回空瓦片
            return None

    except Exception as e:
        print(f"生成栅格瓦片失败: {e}")
        return None


@bp.route('/api/gis-layer-geojson/<int:layer_id>')
def get_gis_layer_geojson(layer_id):
    """获取矢量图层的GeoJSON数据"""
    try:
        if not GIS_LIBS_AVAILABLE:
            return jsonify({'error': 'GIS库不可用'}), 503

        # 获取图层信息
        layer = GISLayer.query.get_or_404(layer_id)

        if layer.layer_type != 'vector':
            return jsonify({'error': '只支持矢量图层'}), 400

        if not os.path.exists(layer.file_path):
            return jsonify({'error': '图层文件不存在'}), 404

        # 读取矢量数据
        import geopandas as gpd
        gdf = gpd.read_file(layer.file_path)

        # 确保坐标系为WGS84
        try:
            # 检查坐标系是否有效
            if gdf.crs is None or str(gdf.crs) == 'EPSG:7' or 'EPSG:7' in str(gdf.crs):
                print(f"警告: 检测到无效坐标系 {gdf.crs}，假设为 EPSG:4326")
                gdf.crs = 'EPSG:4326'
            elif gdf.crs != 'EPSG:4326':
                gdf = gdf.to_crs('EPSG:4326')
        except Exception as e:
            print(f"坐标系转换失败: {e}，假设为 EPSG:4326")
            gdf.crs = 'EPSG:4326'

        # 转换为GeoJSON
        geojson = gdf.to_json()

        from flask import Response
        return Response(geojson, mimetype='application/json')

    except Exception as e:
        print(f"获取GeoJSON失败: {e}")
        return jsonify({'error': '服务器错误'}), 500


# ==================== 辅助函数定义 ====================

def generate_terrain_data(city):
    """
    生成地形数据

    Args:
        city (str): 城市名称

    Returns:
        dict: 地形数据
    """
    # 根据城市生成不同的地形特征
    city_terrain_config = {
        'zhengzhou': {
            'elevation_range': [100, 300],
            'main_terrain': '平原',
            'water_bodies': ['黄河', '金水河', '熊耳河']
        },
        'beijing': {
            'elevation_range': [20, 200],
            'main_terrain': '平原',
            'water_bodies': ['永定河', '潮白河']
        },
        'shanghai': {
            'elevation_range': [0, 50],
            'main_terrain': '平原',
            'water_bodies': ['黄浦江', '苏州河']
        }
    }

    config = city_terrain_config.get(city, city_terrain_config['zhengzhou'])

    # 生成地形图层数据
    terrain_data = {
        'layers': [
            {
                'id': 'elevation',
                'name': '高程图层',
                'type': 'raster',
                'data': {
                    'min_elevation': config['elevation_range'][0],
                    'max_elevation': config['elevation_range'][1],
                    'resolution': '30m',
                    'source': 'DEM数据'
                }
            },
            {
                'id': 'slope',
                'name': '坡度图层',
                'type': 'raster',
                'data': {
                    'slope_classes': ['0-5°', '5-15°', '15-25°', '25-35°', '>35°'],
                    'source': '基于DEM计算'
                }
            },
            {
                'id': 'water_bodies',
                'name': '水体图层',
                'type': 'vector',
                'data': {
                    'rivers': config['water_bodies'],
                    'lakes': [],
                    'source': '水文数据'
                }
            }
        ],
        'metadata': {
            'city': city,
            'main_terrain': config['main_terrain'],
            'coordinate_system': 'WGS84',
            'update_time': datetime.now().isoformat()
        }
    }

    return terrain_data


def get_risk_factors(risk_level, district_name):
    """
    获取风险因子信息

    Args:
        risk_level (str): 风险等级
        district_name (str): 区域名称

    Returns:
        dict: 风险因子信息
    """
    # 基础风险因子模板
    base_factors = {
        'precipitation': {'name': '降水量', 'unit': 'mm'},
        'drainage_density': {'name': '排水密度', 'unit': 'km/km²'},
        'elevation': {'name': '平均高程', 'unit': 'm'},
        'gdp_density': {'name': 'GDP密度', 'unit': '万元/km²'},
        'population_density': {'name': '人口密度', 'unit': '人/km²'}
    }

    # 根据风险等级生成不同的因子值
    risk_multipliers = {
        '极低风险': {'precipitation': 0.2, 'drainage_density': 0.9, 'elevation': 0.8, 'gdp_density': 0.3, 'population_density': 0.3},
        '低风险': {'precipitation': 0.4, 'drainage_density': 0.7, 'elevation': 0.6, 'gdp_density': 0.5, 'population_density': 0.5},
        '中风险': {'precipitation': 0.6, 'drainage_density': 0.5, 'elevation': 0.4, 'gdp_density': 0.7, 'population_density': 0.7},
        '高风险': {'precipitation': 0.8, 'drainage_density': 0.3, 'elevation': 0.2, 'gdp_density': 0.8, 'population_density': 0.8},
        '极高风险': {'precipitation': 1.0, 'drainage_density': 0.1, 'elevation': 0.1, 'gdp_density': 1.0, 'population_density': 1.0}
    }

    multiplier = risk_multipliers.get(risk_level, risk_multipliers['中风险'])

    # 生成具体的因子值
    factors = {}
    for factor_id, factor_info in base_factors.items():
        base_value = random.uniform(0.1, 1.0)
        adjusted_value = base_value * multiplier[factor_id]

        factors[factor_id] = {
            'name': factor_info['name'],
            'value': round(adjusted_value, 3),
            'normalized_value': round(adjusted_value, 3),
            'unit': factor_info['unit'],
            'weight': get_factor_weight(factor_id),
            'contribution': round(adjusted_value * get_factor_weight(factor_id), 3)
        }

    return {
        'district': district_name,
        'risk_level': risk_level,
        'factors': factors,
        'total_score': round(sum(f['contribution'] for f in factors.values()), 3),
        'assessment_time': datetime.now().isoformat()
    }


def get_factor_weight(factor_id):
    """
    获取因子权重

    Args:
        factor_id (str): 因子ID

    Returns:
        float: 权重值
    """
    weights = {
        'precipitation': 0.633,      # 降水量权重
        'drainage_density': 0.260,   # 排水密度权重
        'elevation': 0.107,          # 高程权重
        'gdp_density': 0.6,          # GDP密度权重（影响程度）
        'population_density': 0.4    # 人口密度权重（影响程度）
    }

    return weights.get(factor_id, 0.2)


def generate_mock_china_overview_data():
    """
    生成模拟的全国风险概览数据

    Returns:
        dict: 模拟的全国风险数据
    """
    provinces = [
        '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',
        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',
        '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',
        '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆'
    ]

    risk_levels = ['极低风险', '低风险', '中风险', '高风险', '极高风险']

    mock_data = []
    for province in provinces:
        risk_level = random.choice(risk_levels)
        mock_data.append({
            'province': province,
            'risk_level': risk_level,
            'risk_score': random.uniform(0.1, 1.0),
            'assessment_count': random.randint(1, 10),
            'last_update': datetime.now().isoformat()
        })

    return {
        'success': True,
        'data': mock_data,
        'total_provinces': len(provinces),
        'data_source': 'mock_data'
    }