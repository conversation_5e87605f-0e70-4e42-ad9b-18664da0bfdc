"""
GIS城市内涝灾害风险预警系统 - API路由
"""
import logging
from flask import jsonify, request
from app.api import bp
from app.models import User, Dataset, Role, SystemLog, AnalysisHistory, SystemMetrics, GISLayer
from flask_login import login_required, current_user
from app.online_users import online_tracker
from app.extensions import db
from app.system_monitor import SystemMonitor
from datetime import datetime, timedelta, timezone

# 配置日志
logger = logging.getLogger(__name__)

# 导入服务类
try:
    from app.services.intelligent_classifier import IntelligentClassifier
    INTELLIGENT_CLASSIFIER_AVAILABLE = True
except ImportError as e:
    logger.warning(f"智能分类器不可用: {e}")
    INTELLIGENT_CLASSIFIER_AVAILABLE = False
    IntelligentClassifier = None




from sqlalchemy import func
import json
import os



def get_china_time():
    """获取中国本地时间（UTC+8）"""
    return datetime.now(timezone(timedelta(hours=8)))





@bp.route('/status')
def status():
    """API状态检查"""
    return jsonify({
        'status': 'ok',
        'message': 'GIS Flood Warning System API is running',
        'version': '1.0.0'
    })





@bp.route('/user/profile')
@login_required
def user_profile():
    """获取用户信息"""
    return jsonify({
        'id': current_user.id,
        'username': current_user.username,
        'email': current_user.email,
        'role': current_user.role.name if current_user.role else 'user',
        'created_at': current_user.created_at.isoformat()
    })


@bp.route('/user/data-overview')
@login_required
def user_data_overview():
    """获取用户数据概览"""
    try:
        # 用户的数据集数量
        dataset_count = Dataset.query.filter_by(created_by=current_user.id).count()

        # 用户数据集的总大小
        user_datasets = Dataset.query.filter_by(created_by=current_user.id).all()
        total_size = sum(dataset.file_size or 0 for dataset in user_datasets)

        # 数据类型数量
        type_count = db.session.query(Dataset.data_type).filter_by(created_by=current_user.id).distinct().count()

        # 最近7天上传的数据集数量
        seven_days_ago = get_china_time() - timedelta(days=7)
        recent_uploads = Dataset.query.filter(
            Dataset.created_by == current_user.id,
            Dataset.created_at >= seven_days_ago
        ).count()

        return jsonify({
            'success': True,
            'data': {
                'dataset_count': dataset_count,
                'total_size': total_size,
                'type_count': type_count,
                'recent_uploads': recent_uploads
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取数据概览失败: {str(e)}'
        }), 500


@bp.route('/user/data-type-stats')
@login_required
def user_data_type_stats():
    """获取用户数据类型统计"""
    try:
        # 按数据类型统计用户的数据集
        type_stats = db.session.query(
            Dataset.data_type,
            func.count(Dataset.id)
        ).filter_by(created_by=current_user.id).group_by(Dataset.data_type).all()

        result = [{'type': data_type, 'count': count} for data_type, count in type_stats]

        return jsonify({
            'success': True,
            'data': result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取数据类型统计失败: {str(e)}'
        }), 500



@bp.route('/profile/update', methods=['POST'])
@login_required
def update_profile():
    """更新个人资料"""
    data = request.get_json()

    try:
        # 验证当前密码（如果要修改密码）
        if data.get('new_password'):
            if not data.get('current_password'):
                return jsonify({
                    'success': False,
                    'message': '修改密码时必须输入当前密码'
                }), 400

            if not current_user.check_password(data['current_password']):
                return jsonify({
                    'success': False,
                    'message': '当前密码错误'
                }), 400

        # 检查用户名是否已被其他用户使用
        if 'username' in data and data['username'] != current_user.username:
            existing_user = User.query.filter_by(username=data['username']).first()
            if existing_user:
                return jsonify({
                    'success': False,
                    'message': '用户名已存在'
                }), 400

        # 检查邮箱是否已被其他用户使用
        if 'email' in data and data['email'] != current_user.email:
            existing_user = User.query.filter_by(email=data['email']).first()
            if existing_user:
                return jsonify({
                    'success': False,
                    'message': '邮箱已存在'
                }), 400

        # 更新基本信息
        if 'username' in data:
            current_user.username = data['username']

        if 'email' in data:
            current_user.email = data['email']

        # 更新密码
        if data.get('new_password'):
            current_user.set_password(data['new_password'])

        # 更新时间戳
        current_user.updated_at = get_china_time()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '个人资料更新成功',
            'data': {
                'username': current_user.username,
                'email': current_user.email
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500


@bp.route('/datasets')
@login_required
def list_datasets():
    """获取数据集列表"""
    try:
        datasets = Dataset.query.all()
        return jsonify({
            'success': True,
            'data': [{
                'id': dataset.id,
                'name': dataset.name,
                'description': dataset.description,
                'data_type': dataset.data_type,
                'file_path': dataset.file_path,
                'file_size': dataset.file_size,
                'coordinate_system': dataset.coordinate_system,
                'feature_count': dataset.feature_count,
                'is_raster': dataset.is_raster,
                'created_at': dataset.created_at.isoformat() if dataset.created_at else None,
                'created_by': dataset.creator.username if dataset.creator else None
            } for dataset in datasets]
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取数据集列表失败: {str(e)}'
        }), 500


@bp.route('/datasets/upload', methods=['POST'])
@login_required
def upload_dataset():
    """上传数据集"""
    import os
    import shutil
    from werkzeug.utils import secure_filename
    from flask import current_app
    from app.services.data_processing import DataProcessingService
    from app.services.gis_data_service import GISDataService

    try:
        # 检查是否有文件上传
        if 'files' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        # 获取表单数据
        dataset_name = request.form.get('name', '').strip()
        dataset_description = request.form.get('description', '').strip()
        data_type = request.form.get('data_type', 'unknown').strip()

        if not dataset_name:
            return jsonify({'success': False, 'error': '请输入数据集名称'}), 400

        # 检查数据集名称是否已存在
        existing = Dataset.query.filter_by(name=dataset_name).first()
        if existing:
            return jsonify({'success': False, 'error': '数据集名称已存在'}), 400

        # 创建基础存储结构
        base_path = os.path.join(current_app.root_path, '..', 'model_data')
        DataProcessingService.create_storage_structure(base_path)

        # 创建数据集专用文件夹
        dataset_folder = os.path.join(base_path, 'datasets', secure_filename(dataset_name))
        os.makedirs(dataset_folder, exist_ok=True)

        uploaded_files = []
        total_size = 0

        # 保存上传的文件，无任何限制
        for file in files:
            if file.filename:
                # 保持原始文件名，只进行基本的安全处理
                filename = secure_filename(file.filename) or f"unnamed_file_{len(uploaded_files)}"
                file_path = os.path.join(dataset_folder, filename)

                # 如果文件已存在，添加序号
                counter = 1
                original_path = file_path
                while os.path.exists(file_path):
                    name, ext = os.path.splitext(original_path)
                    file_path = f"{name}_{counter}{ext}"
                    counter += 1

                # 保存文件
                file.save(file_path)

                file_size = os.path.getsize(file_path)
                file_type = DataProcessingService.detect_file_type(filename)
                file_ext = os.path.splitext(filename)[1].lower()

                uploaded_files.append({
                    'name': os.path.basename(file_path),
                    'original_name': file.filename,
                    'path': file_path,
                    'size': file_size,
                    'type': file_type,
                    'extension': file_ext
                })
                total_size += file_size

        # 智能检测数据类型
        if data_type == 'unknown':
            # 根据文件类型智能推断数据集类型
            file_types = [f['type'] for f in uploaded_files]
            if 'vector' in file_types or 'geojson' in file_types:
                data_type = 'vector'
            elif 'raster' in file_types:
                data_type = 'raster'
            elif 'dem' in file_types:
                data_type = 'dem'
            else:
                data_type = 'other'

        # 创建数据集记录
        dataset = Dataset(
            name=dataset_name,
            description=dataset_description,
            data_type=data_type,
            file_path=dataset_folder,
            file_size=total_size,
            created_by=current_user.id,
            processing_status='uploaded'
        )

        # 组织文件到分类存储
        try:
            organized_files = DataProcessingService.organize_files(dataset, uploaded_files)
            dataset.processing_log = f'文件已组织分类: {json.dumps(organized_files, ensure_ascii=False)}'
        except Exception as e:
            print(f"文件组织失败: {e}")
            dataset.processing_log = f'文件组织失败: {str(e)}'

        # 更新处理状态
        dataset.processing_status = 'analyzing'
        dataset.processing_log = '正在分析上传的文件...'
        db.session.commit()

        # 尝试分析文件获取更多信息
        try:
            print(f"📊 开始分析数据集文件: {dataset.name}")
            analyze_dataset_files(dataset, uploaded_files)
            dataset.processing_log = '文件分析完成'
            print(f"✅ 文件分析完成: {dataset.name}")
        except Exception as e:
            print(f"❌ 文件分析失败: {e}")
            dataset.processing_log = f'文件分析失败: {str(e)}'

        db.session.add(dataset)
        db.session.commit()

        # 记录操作日志
        SystemMonitor.log_action(
            action='上传了数据集',
            target=dataset.name,
            details=f'包含 {len(uploaded_files)} 个文件，总大小 {total_size} 字节，类型: {data_type}'
        )

        # 增强的土地利用数据自动处理
        landuse_results = []
        landuse_files_count = 0
        processing_steps = []

        # 创建日志捕获器
        import io
        import sys
        from contextlib import redirect_stdout, redirect_stderr

        log_capture = io.StringIO()

        # 先统计土地利用文件数量
        try:
            from app.services.enhanced_landuse_processor import enhanced_landuse_processor

            for file_info in uploaded_files:
                filename = file_info['name']
                if enhanced_landuse_processor.is_landuse_file(filename):
                    landuse_files_count += 1

            if landuse_files_count > 0:
                step_msg = f"检测到 {landuse_files_count} 个土地利用文件，开始处理..."
                print(f"🌱 {step_msg}")
                processing_steps.append(f"🌱 {step_msg}")
                dataset.processing_status = 'processing_landuse'
                dataset.processing_log = f'正在处理 {landuse_files_count} 个土地利用文件...'
                db.session.commit()

                # 处理每个土地利用文件
                processed_count = 0
                for file_info in uploaded_files:
                    file_path = file_info['path']
                    filename = file_info['name']

                    # 检查是否为土地利用文件
                    if enhanced_landuse_processor.is_landuse_file(filename):
                        processed_count += 1
                        step_msg = f"处理土地利用文件 ({processed_count}/{landuse_files_count}): {filename}"
                        print(f"🔄 {step_msg}")
                        processing_steps.append(f"🔄 {step_msg}")

                        # 更新处理状态
                        dataset.processing_log = f'正在处理土地利用文件 ({processed_count}/{landuse_files_count}): {filename}'
                        db.session.commit()

                        # 创建日志处理器来捕获详细日志
                        import logging
                        from io import StringIO

                        # 创建字符串流来捕获日志
                        log_stream = StringIO()
                        log_handler = logging.StreamHandler(log_stream)
                        log_handler.setLevel(logging.INFO)

                        # 添加到相关的logger
                        enhanced_logger = logging.getLogger('app.services.enhanced_landuse_processor')
                        landuse_logger = logging.getLogger('app.services.landuse_auto_processor')
                        gis_logger = logging.getLogger('app.services.gis_data_service')

                        enhanced_logger.addHandler(log_handler)
                        landuse_logger.addHandler(log_handler)
                        gis_logger.addHandler(log_handler)

                        try:
                            # 增强处理土地利用文件
                            result = enhanced_landuse_processor.process_uploaded_landuse_file(
                                file_path, filename, current_user.id, processing_steps
                            )

                            # 获取捕获的日志
                            captured_logs = log_stream.getvalue()
                            if captured_logs:
                                # 将日志按行分割并添加到处理步骤
                                log_lines = captured_logs.strip().split('\n')
                                for log_line in log_lines:
                                    if log_line.strip():
                                        processing_steps.append(log_line.strip())

                            if result:
                                landuse_results.append(result)
                                success_msg = f"土地利用文件处理成功: {result['layer_name']}"
                                print(f"✅ {success_msg}")
                                processing_steps.append(f"✅ {success_msg}")
                            else:
                                error_msg = f"土地利用文件处理失败: {filename}"
                                print(f"❌ {error_msg}")
                                processing_steps.append(f"❌ {error_msg}")

                        finally:
                            # 移除日志处理器
                            enhanced_logger.removeHandler(log_handler)
                            landuse_logger.removeHandler(log_handler)
                            gis_logger.removeHandler(log_handler)
                            log_handler.close()

                if landuse_results:
                    final_msg = f'成功处理了 {len(landuse_results)} 个土地利用文件'
                    dataset.processing_log = final_msg
                    processing_steps.append(f"🎉 {final_msg}")
                    print(f"🎉 土地利用文件处理完成: {len(landuse_results)}/{landuse_files_count}")
                else:
                    final_msg = '土地利用文件处理完成，但未生成有效结果'
                    dataset.processing_log = final_msg
                    processing_steps.append(f"⚠️ {final_msg}")
                    print("⚠️ 土地利用文件处理完成，但未生成有效结果")

                db.session.commit()

        except Exception as e:
            error_msg = f"土地利用数据处理失败: {str(e)}"
            print(f"❌ {error_msg}")
            dataset.processing_log = error_msg
            db.session.commit()

        # 最终状态更新
        dataset.processing_status = 'completed'
        final_log_parts = [f'上传完成: {len(uploaded_files)} 个文件']
        if landuse_results:
            final_log_parts.append(f'处理了 {len(landuse_results)} 个土地利用文件')
        dataset.processing_log = '，'.join(final_log_parts)
        db.session.commit()

        # 构建响应消息
        message_parts = [f'✅ 成功上传 {len(uploaded_files)} 个文件']
        if landuse_results:
            message_parts.append(f'🌱 自动处理了 {len(landuse_results)} 个土地利用文件')
            message_parts.append('📊 生成了对应的地图和统计图表')

        print(f"🎉 数据集上传和处理完成: {dataset.name}")

        return jsonify({
            'success': True,
            'message': '，'.join(message_parts),
            'data': {
                'id': dataset.id,
                'name': dataset.name,
                'files_count': len(uploaded_files),
                'total_size': total_size,
                'data_type': data_type,
                'files': uploaded_files,
                'landuse_results': landuse_results,
                'processing_status': dataset.processing_status,
                'processing_log': dataset.processing_log,
                'processing_steps': processing_steps
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': f'上传失败: {str(e)}'}), 500


@bp.route('/gis-layers/upload', methods=['POST'])
@login_required
def upload_gis_layers():
    """上传GIS图层文件（Shapefile和TIFF）"""
    import os
    import shutil
    from datetime import datetime
    from werkzeug.utils import secure_filename
    from flask import current_app
    from app.models import GISLayer

    try:
        # 检查是否有文件上传
        if 'files' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        # 创建上传目录
        upload_base = os.path.join(current_app.root_path, '..', 'data', 'gis_layers')
        os.makedirs(upload_base, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        upload_dir = os.path.join(upload_base, f'upload_{timestamp}')
        os.makedirs(upload_dir, exist_ok=True)

        # 保存上传的文件
        uploaded_files = []
        for file in files:
            if file.filename:
                filename = secure_filename(file.filename)
                if not filename:
                    continue

                file_path = os.path.join(upload_dir, filename)
                file.save(file_path)

                file_size = os.path.getsize(file_path)
                uploaded_files.append({
                    'name': filename,
                    'path': file_path,
                    'size': file_size
                })

        if not uploaded_files:
            return jsonify({'success': False, 'error': '没有有效的文件'}), 400

        # 使用自动图层服务处理文件
        from app.services.auto_layer_service import AutoLayerService

        result = AutoLayerService.process_uploaded_files(uploaded_files, upload_dir, current_user.id)

        processed_layers = []
        errors = result.get('errors', [])

        # 获取处理后的图层对象
        for layer_info in result.get('processed_layers', []):
            layer = GISLayer.query.get(layer_info['id'])
            if layer:
                processed_layers.append(layer)

        # 记录操作日志
        SystemMonitor.log_action(
            action='上传了GIS图层',
            target=f'{len(processed_layers)}个图层',
            details=f'成功: {len(processed_layers)}, 错误: {len(errors)}'
        )

        return jsonify({
            'success': result['success'],
            'message': result['message'],
            'data': {
                'processed_layers': [layer.to_dict() for layer in processed_layers],
                'layer_summary': result.get('processed_layers', []),
                'errors': errors,
                'upload_dir': upload_dir,
                'auto_processed': True
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': f'上传失败: {str(e)}'}), 500


@bp.route('/trigger-auto-import', methods=['POST'])
@login_required
def trigger_auto_import():
    """手动触发自动数据导入"""
    try:
        from app.services.auto_layer_service import AutoLayerService

        # 触发完整的数据导入
        AutoLayerService._trigger_full_data_import()

        # 记录操作日志
        SystemMonitor.log_action(
            action='手动触发自动数据导入',
            target='全部数据',
            details='执行完整的土地利用和行政区划数据导入'
        )

        return jsonify({
            'success': True,
            'message': '自动数据导入已完成，请查看日志了解详细信息'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'触发自动导入失败: {str(e)}'
        }), 500


@bp.route('/provinces-with-data', methods=['GET'])
def get_provinces_with_data():
    """获取有数据的省份列表"""
    try:
        from app.services.auto_layer_service import AutoLayerService

        provinces = AutoLayerService.get_provinces_with_data()

        return jsonify({
            'success': True,
            'data': provinces,
            'count': len(provinces)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'获取省份列表失败: {str(e)}'}), 500


@bp.route('/province-layers/<province>', methods=['GET'])
def get_province_layers(province):
    """获取指定省份的图层信息"""
    try:
        from app.services.auto_layer_service import AutoLayerService

        layers = AutoLayerService.get_available_layers_by_province(province)

        return jsonify({
            'success': True,
            'data': layers,
            'province': province
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'获取省份图层失败: {str(e)}'}), 500


@bp.route('/gis-layers', methods=['GET'])
@login_required
def get_gis_layers():
    """获取GIS图层列表"""
    try:
        # 获取查询参数
        layer_type = request.args.get('layer_type')  # 'vector' 或 'raster'
        data_category = request.args.get('data_category')  # 'administrative' 或 'landuse'
        province = request.args.get('province')

        # 构建查询
        query = GISLayer.query

        if layer_type:
            query = query.filter(GISLayer.layer_type == layer_type)
        if data_category:
            query = query.filter(GISLayer.data_category == data_category)
        if province:
            query = query.filter(GISLayer.province == province)

        # 按创建时间倒序排列
        layers = query.order_by(GISLayer.created_at.desc()).all()

        return jsonify({
            'success': True,
            'data': [layer.to_dict() for layer in layers],
            'count': len(layers)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/gis-layers/<int:layer_id>/geojson', methods=['GET'])
@login_required
def get_layer_geojson(layer_id):
    """获取矢量图层的GeoJSON数据"""
    try:
        layer = GISLayer.query.get_or_404(layer_id)

        if layer.layer_type != 'vector':
            return jsonify({'success': False, 'error': '只有矢量图层支持GeoJSON格式'}), 400

        if not os.path.exists(layer.file_path):
            return jsonify({'success': False, 'error': '图层文件不存在'}), 404

        # 使用GeoPandas读取并转换为GeoJSON
        import geopandas as gpd
        gdf = gpd.read_file(layer.file_path)

        # 转换为GeoJSON格式
        geojson = json.loads(gdf.to_json())

        return jsonify({
            'success': True,
            'data': geojson,
            'layer_info': {
                'id': layer.id,
                'name': layer.name,
                'display_name': layer.display_name,
                'data_category': layer.data_category,
                'province': layer.province,
                'feature_count': layer.feature_count,
                'geometry_type': layer.geometry_type
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@bp.route('/gis-layers/<int:layer_id>/delete', methods=['DELETE'])
@login_required
def delete_gis_layer(layer_id):
    """删除GIS图层"""
    try:
        layer = GISLayer.query.get_or_404(layer_id)

        # 检查权限
        if layer.created_by != current_user.id and not current_user.is_admin():
            return jsonify({'success': False, 'error': '权限不足'}), 403

        # 删除文件
        try:
            if os.path.exists(layer.file_path):
                os.remove(layer.file_path)

            # 删除相关文件
            related_files = layer.get_related_files_list()
            for file_path in related_files:
                if os.path.exists(file_path):
                    os.remove(file_path)
        except Exception as e:
            logger.warning(f"删除文件失败: {e}")

        # 删除数据库记录
        db.session.delete(layer)
        db.session.commit()

        # 记录操作日志
        SystemMonitor.log_action(
            action='删除了GIS图层',
            target=layer.name,
            details=f'图层类型: {layer.layer_type}, 数据类别: {layer.data_category}'
        )

        return jsonify({
            'success': True,
            'message': '图层删除成功'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500






def analyze_dataset_files(dataset, files):
    """分析数据集文件，提取元信息并进行智能分类"""
    import os
    import json

    # 统计文件类型
    file_types = {}
    classification_results = []

    for file_info in files:
        ext = os.path.splitext(file_info['name'])[1].lower()
        file_types[ext] = file_types.get(ext, 0) + 1

        # 对栅格文件进行智能分类
        if ext in ['.tif', '.tiff'] and os.path.exists(file_info['path']) and INTELLIGENT_CLASSIFIER_AVAILABLE:
            try:
                classification = IntelligentClassifier.get_enhanced_classification_info(
                    file_info['path'], file_info['name']
                )
                classification_results.append({
                    'filename': file_info['name'],
                    'classification': classification
                })

                # 记录分类信息到日志
                print(f"智能分类结果 - {file_info['name']}: "
                      f"省份={classification['classification'].get('province')}, "
                      f"类型={classification['classification'].get('data_type')}, "
                      f"置信度={classification['classification'].get('confidence', 0.0):.2f}")

            except Exception as e:
                print(f"文件 {file_info['name']} 智能分类失败: {e}")
        elif ext in ['.tif', '.tiff'] and not INTELLIGENT_CLASSIFIER_AVAILABLE:
            print(f"智能分类器不可用，跳过文件 {file_info['name']} 的分类")

    # 设置特征数量（简单估算）
    dataset.feature_count = len(files)

    # 根据文件类型推断数据类型
    if '.shp' in file_types:
        dataset.data_type = 'vector'
    elif '.tif' in file_types or '.tiff' in file_types:
        dataset.data_type = 'raster'
    elif '.geojson' in file_types:
        dataset.data_type = 'geojson'

    # 如果有智能分类结果，更新数据集描述
    if classification_results:
        # 提取最可信的分类结果
        best_classification = max(
            classification_results,
            key=lambda x: x['classification']['classification'].get('confidence', 0.0)
        )

        classification_info = best_classification['classification']['classification']

        # 更新数据集描述，包含智能分类信息
        description_parts = []
        if dataset.description:
            description_parts.append(dataset.description)

        if classification_info.get('province'):
            description_parts.append(f"省份: {classification_info['province']}")

        if classification_info.get('data_type') != 'other':
            description_parts.append(f"数据类型: {classification_info.get('description', classification_info['data_type'])}")

        if classification_info.get('confidence', 0.0) > 0.5:
            description_parts.append(f"分类置信度: {classification_info['confidence']:.2f}")

        # 添加建议标签
        suggested_tags = best_classification['classification'].get('suggested_tags', [])
        if suggested_tags:
            description_parts.append(f"建议标签: {', '.join(suggested_tags)}")

        dataset.description = " | ".join(description_parts)

        # 将完整的分类结果保存到处理日志中
        if dataset.processing_log:
            dataset.processing_log += f"\n\n智能分类结果: {json.dumps(classification_results, ensure_ascii=False, indent=2)}"
        else:
            dataset.processing_log = f"智能分类结果: {json.dumps(classification_results, ensure_ascii=False, indent=2)}"


@bp.route('/datasets/<int:dataset_id>', methods=['GET'])
@login_required
def get_dataset(dataset_id):
    """获取数据集基本信息"""
    try:
        dataset = Dataset.query.get_or_404(dataset_id)
        return jsonify({
            'success': True,
            'data': {
                'id': dataset.id,
                'name': dataset.name,
                'description': dataset.description,
                'data_type': dataset.data_type,
                'file_path': dataset.file_path,
                'file_size': dataset.file_size,
                'coordinate_system': dataset.coordinate_system,
                'feature_count': dataset.feature_count,
                'is_raster': dataset.is_raster,
                'created_at': dataset.created_at.isoformat() if dataset.created_at else None,
                'updated_at': dataset.updated_at.isoformat() if dataset.updated_at else None,
                'created_by': dataset.creator.username if dataset.creator else None
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取数据集信息失败: {str(e)}'
        }), 500


@bp.route('/datasets/<int:dataset_id>/classify', methods=['POST'])
@login_required
def classify_dataset(dataset_id):
    """对数据集进行智能分类分析"""
    try:
        # 检查智能分类器是否可用
        if not INTELLIGENT_CLASSIFIER_AVAILABLE:
            return jsonify({
                'success': False,
                'error': '智能分类器不可用，请检查GIS库是否正确安装'
            }), 503

        dataset = Dataset.query.get_or_404(dataset_id)

        # 检查权限
        if not current_user.is_admin() and dataset.created_by != current_user.id:
            return jsonify({'success': False, 'error': '权限不足'}), 403

        if not dataset.file_path or not os.path.exists(dataset.file_path):
            return jsonify({'success': False, 'error': '数据集文件不存在'}), 404

        classification_results = []

        # 遍历数据集文件夹中的所有文件
        for root, dirs, files in os.walk(dataset.file_path):
            for filename in files:
                file_path = os.path.join(root, filename)
                file_ext = os.path.splitext(filename)[1].lower()

                # 只对栅格文件进行智能分类
                if file_ext in ['.tif', '.tiff']:
                    try:
                        classification = IntelligentClassifier.get_enhanced_classification_info(
                            file_path, filename
                        )
                        classification_results.append({
                            'filename': filename,
                            'file_path': file_path,
                            'classification': classification
                        })
                    except Exception as e:
                        classification_results.append({
                            'filename': filename,
                            'file_path': file_path,
                            'error': str(e)
                        })

        if not classification_results:
            return jsonify({
                'success': False,
                'error': '未找到可分类的栅格文件'
            }), 404

        # 更新数据集信息
        try:
            # 找到最可信的分类结果
            valid_results = [r for r in classification_results if 'classification' in r]
            if valid_results:
                best_result = max(
                    valid_results,
                    key=lambda x: x['classification']['classification'].get('confidence', 0.0)
                )

                classification_info = best_result['classification']['classification']

                # 更新数据集描述
                description_parts = []
                if dataset.description and not any(keyword in dataset.description for keyword in ['省份:', '数据类型:', '分类置信度:']):
                    description_parts.append(dataset.description)

                if classification_info.get('province'):
                    description_parts.append(f"省份: {classification_info['province']}")

                if classification_info.get('data_type') != 'other':
                    description_parts.append(f"数据类型: {classification_info.get('description', classification_info['data_type'])}")

                if classification_info.get('confidence', 0.0) > 0.5:
                    description_parts.append(f"分类置信度: {classification_info['confidence']:.2f}")

                dataset.description = " | ".join(description_parts)
                db.session.commit()

        except Exception as e:
            print(f"更新数据集信息失败: {e}")

        return jsonify({
            'success': True,
            'message': f'成功分析了 {len(classification_results)} 个文件',
            'data': {
                'dataset_id': dataset_id,
                'dataset_name': dataset.name,
                'classification_results': classification_results,
                'summary': {
                    'total_files': len(classification_results),
                    'successful_classifications': len([r for r in classification_results if 'classification' in r]),
                    'failed_classifications': len([r for r in classification_results if 'error' in r])
                }
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'分类分析失败: {str(e)}'
        }), 500


@bp.route('/datasets/<int:dataset_id>', methods=['DELETE'])
@login_required
def delete_dataset(dataset_id):
    """删除数据集"""
    import os
    import shutil

    try:
        dataset = Dataset.query.get_or_404(dataset_id)

        # 检查权限
        if not current_user.is_admin() and dataset.created_by != current_user.id:
            return jsonify({'success': False, 'error': '权限不足'}), 403

        # 保存数据集信息用于后续操作
        dataset_name = dataset.name
        dataset_file_path = dataset.file_path

        # 删除相关的数据处理历史记录
        from app.models import DataProcessingHistory
        try:
            # 使用SQL直接删除，避免ORM级联问题
            deleted_count = db.session.execute(
                "DELETE FROM data_processing_history WHERE dataset_id = :dataset_id",
                {"dataset_id": dataset_id}
            ).rowcount
            print(f"删除了数据集 {dataset_id} 的 {deleted_count} 条处理历史记录")
        except Exception as history_error:
            print(f"删除处理历史记录时出错: {history_error}")
            # 继续执行，不因为历史记录删除失败而中断

        # 删除相关的分析历史记录（如果有引用）
        try:
            from app.models import AnalysisHistory
            # 使用SQL更新分析历史记录，移除对此数据集的引用
            db.session.execute(
                "UPDATE analysis_history SET input_datasets = REPLACE(input_datasets, :old_ref, '') WHERE input_datasets LIKE :pattern",
                {
                    "old_ref": f'"{dataset_id}"',
                    "pattern": f'%"{dataset_id}"%'
                }
            )
            print(f"更新了分析历史记录中对数据集 {dataset_id} 的引用")
        except Exception as analysis_error:
            print(f"更新分析历史记录时出错: {analysis_error}")
            # 继续执行，不因为分析历史更新失败而中断

        # 使用SQL直接删除数据库记录
        try:
            # 删除数据集记录（外键约束会自动删除相关的处理历史记录）
            db.session.execute(
                "DELETE FROM datasets WHERE id = :id",
                {"id": dataset_id}
            )

            db.session.commit()
            print(f"成功删除数据集: {dataset_name} (ID: {dataset_id})")

        except Exception as delete_error:
            print(f"数据库删除失败: {delete_error}")
            db.session.rollback()
            raise delete_error

        # 删除文件（在数据库删除成功后）
        if dataset_file_path and os.path.exists(dataset_file_path):
            try:
                if os.path.isdir(dataset_file_path):
                    shutil.rmtree(dataset_file_path)
                else:
                    os.remove(dataset_file_path)
                print(f"删除了数据集文件: {dataset_file_path}")
            except Exception as file_error:
                print(f"删除文件时出错: {file_error}")
                # 文件删除失败不影响整体操作

        # 记录操作日志
        try:
            SystemMonitor.log_action(
                action='删除了数据集',
                target=dataset_name,
                details=f'数据集ID: {dataset_id}'
            )
        except Exception as log_error:
            print(f"记录操作日志时出错: {log_error}")

        return jsonify({'success': True, 'message': '数据集删除成功'})

    except Exception as e:
        db.session.rollback()
        print(f"删除数据集失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': f'删除失败: {str(e)}'}), 500





@bp.route('/datasets/stats')
@login_required
def get_datasets_stats():
    """获取数据集统计信息"""
    try:
        total_datasets = Dataset.query.count()
        user_datasets = Dataset.query.filter_by(created_by=current_user.id).count()

        # 按类型统计
        type_stats = db.session.query(
            Dataset.data_type,
            func.count(Dataset.id)
        ).group_by(Dataset.data_type).all()

        # 计算总文件大小
        total_size = db.session.query(func.sum(Dataset.file_size)).scalar() or 0

        return jsonify({
            'success': True,
            'data': {
                'total_datasets': total_datasets,
                'user_datasets': user_datasets,
                'total_size': total_size,
                'type_stats': [{'type': t[0], 'count': t[1]} for t in type_stats]
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': f'获取统计失败: {str(e)}'}), 500


@bp.route('/datasets/import-folder', methods=['POST'])
@login_required
def import_folder():
    """导入文件夹中的数据 - 移除所有限制"""
    import os
    import shutil
    from werkzeug.utils import secure_filename
    from flask import current_app
    from app.services.data_processing import DataProcessingService

    try:
        data = request.get_json()
        folder_path = data.get('folder_path', '').strip()
        dataset_name = data.get('name', '').strip()
        dataset_description = data.get('description', '').strip()
        data_type = data.get('data_type', 'unknown').strip()

        if not folder_path or not dataset_name:
            return jsonify({'success': False, 'error': '请提供文件夹路径和数据集名称'}), 400

        # 检查文件夹是否存在
        if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
            return jsonify({'success': False, 'error': '指定的文件夹不存在'}), 400

        # 检查数据集名称是否已存在
        existing = Dataset.query.filter_by(name=dataset_name).first()
        if existing:
            return jsonify({'success': False, 'error': '数据集名称已存在'}), 400

        # 创建基础存储结构
        base_path = os.path.join(current_app.root_path, '..', 'model_data')
        DataProcessingService.create_storage_structure(base_path)

        # 创建数据集专用文件夹
        dataset_folder = os.path.join(base_path, 'datasets', secure_filename(dataset_name))

        # 如果目标文件夹已存在，添加时间戳
        if os.path.exists(dataset_folder):
            import time
            timestamp = str(int(time.time()))
            dataset_folder = f"{dataset_folder}_{timestamp}"

        # 复制文件夹，支持任意深度和任意文件类型
        shutil.copytree(folder_path, dataset_folder)

        # 统计文件信息并分类
        total_size = 0
        file_count = 0
        file_info_list = []

        for root, dirs, files in os.walk(dataset_folder):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    file_size = os.path.getsize(file_path)
                    file_type = DataProcessingService.detect_file_type(file)
                    file_ext = os.path.splitext(file)[1].lower()

                    file_info_list.append({
                        'name': file,
                        'path': file_path,
                        'size': file_size,
                        'type': file_type,
                        'extension': file_ext,
                        'relative_path': os.path.relpath(file_path, dataset_folder)
                    })

                    total_size += file_size
                    file_count += 1
                except (OSError, IOError):
                    continue

        # 智能检测数据类型
        if data_type == 'unknown':
            file_types = [f['type'] for f in file_info_list]
            if 'vector' in file_types or 'geojson' in file_types:
                data_type = 'vector'
            elif 'raster' in file_types:
                data_type = 'raster'
            elif 'dem' in file_types:
                data_type = 'dem'
            else:
                data_type = 'other'

        # 创建数据集记录
        dataset = Dataset(
            name=dataset_name,
            description=dataset_description,
            data_type=data_type,
            file_path=dataset_folder,
            file_size=total_size,
            feature_count=file_count,
            created_by=current_user.id,
            processing_status='uploaded'
        )

        # 组织文件到分类存储
        try:
            organized_files = DataProcessingService.organize_files(dataset, file_info_list)
            dataset.processing_log = f'文件夹导入完成，文件已组织分类: {json.dumps(organized_files, ensure_ascii=False)}'
        except Exception as e:
            print(f"文件组织失败: {e}")
            dataset.processing_log = f'文件组织失败: {str(e)}'

        # 尝试分析文件获取更多信息
        try:
            analyze_folder_contents(dataset, dataset_folder)
        except Exception as e:
            print(f"文件夹分析失败: {e}")

        db.session.add(dataset)
        db.session.commit()

        # 记录操作日志
        SystemMonitor.log_action(
            action='导入了文件夹',
            target=dataset.name,
            details=f'包含 {file_count} 个文件，总大小 {total_size} 字节，类型: {data_type}'
        )

        return jsonify({
            'success': True,
            'message': f'成功导入文件夹，包含 {file_count} 个文件',
            'data': {
                'id': dataset.id,
                'name': dataset.name,
                'files_count': file_count,
                'total_size': total_size,
                'data_type': data_type,
                'target_path': dataset_folder,
                'files': file_info_list[:10]  # 只返回前10个文件信息
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': f'导入失败: {str(e)}'}), 500


def analyze_folder_contents(dataset, folder_path):
    """分析文件夹内容，提取元信息"""
    import os

    # 统计文件类型
    file_types = {}
    vector_files = []
    raster_files = []

    for root, dirs, files in os.walk(folder_path):
        for file in files:
            ext = os.path.splitext(file)[1].lower()
            file_types[ext] = file_types.get(ext, 0) + 1

            # 分类文件
            if ext in ['.shp', '.geojson', '.kml', '.kmz']:
                vector_files.append(file)
            elif ext in ['.tif', '.tiff']:
                raster_files.append(file)

    # 根据文件类型推断数据类型
    if '.shp' in file_types:
        dataset.data_type = 'vector'
    elif '.tif' in file_types or '.tiff' in file_types:
        dataset.data_type = 'raster'
        dataset.is_raster = True
    elif '.geojson' in file_types:
        dataset.data_type = 'geojson'

    # 如果包含DEM相关文件，标记为DEM
    dem_keywords = ['dem', 'elevation', 'height', '高程', '海拔']
    folder_name_lower = os.path.basename(folder_path).lower()
    if any(keyword in folder_name_lower for keyword in dem_keywords):
        dataset.data_type = 'dem'
        dataset.is_raster = True


# 系统监控相关API
@bp.route('/online-users')
def online_users_stats():
    """获取在线用户统计"""
    stats = online_tracker.get_user_stats()
    return jsonify(stats)


@bp.route('/online-count')
def online_count():
    """获取在线用户数量"""
    stats = {
        'total': online_tracker.get_online_count(),
        'logged_in': online_tracker.get_logged_in_count(),
        'anonymous': online_tracker.get_anonymous_count()
    }
    print(f"API调用 /online-count: {stats}")
    return jsonify(stats)


@bp.route('/admin/users')
@login_required
def admin_users_list():
    """获取用户列表 - 仅管理员可访问"""
    if not current_user.is_admin():
        return jsonify({'error': '权限不足'}), 403

    users = User.query.all()
    users_data = []

    for user in users:
        users_data.append({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'role': user.role.name if user.role else 'user',
            'is_active': user.is_active,
            'created_at': user.created_at.strftime('%Y-%m-%d') if user.created_at else '',
            'last_login': user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else '从未登录'
        })

    return jsonify({
        'success': True,
        'data': users_data
    })


@bp.route('/admin/users/stats')
@login_required
def admin_users_stats():
    """获取用户统计信息 - 仅管理员可访问"""
    if not current_user.is_admin():
        return jsonify({'error': '权限不足'}), 403

    # 总用户数
    total_users = User.query.count()

    # 活跃用户数（is_active=True）
    active_users = User.query.filter_by(is_active=True).count()

    # 管理员数量
    admin_role = Role.query.filter_by(name='admin').first()
    admin_count = User.query.filter_by(role_id=admin_role.id).count() if admin_role else 0

    # 今日新增用户数
    today = get_china_time().date()
    today_new_users = User.query.filter(
        func.date(User.created_at) == today
    ).count()

    return jsonify({
        'success': True,
        'data': {
            'total_users': total_users,
            'active_users': active_users,
            'admin_count': admin_count,
            'today_new_users': today_new_users
        }
    })


# 可视化模块相关API
@bp.route('/visualization-module/management-stats')
@login_required
def visualization_module_management_stats():
    """获取可视化模块的管理统计信息"""
    try:
        # 地图访问统计
        map_views = SystemLog.query.filter(
            SystemLog.action.like('%地图%')
        ).count()

        # 图表生成统计
        chart_generations = SystemLog.query.filter(
            SystemLog.action.like('%图表%')
        ).count()

        # 分析任务统计
        analysis_count = AnalysisHistory.query.count()
        user_analysis_count = AnalysisHistory.query.filter_by(user_id=current_user.id).count() if not current_user.is_admin() else analysis_count

        # 按分析类型统计
        analysis_type_stats = db.session.query(
            AnalysisHistory.analysis_type,
            func.count(AnalysisHistory.id)
        ).group_by(AnalysisHistory.analysis_type).all()

        # 最近分析任务
        recent_analysis = AnalysisHistory.query.order_by(AnalysisHistory.created_at.desc()).limit(10).all()

        return jsonify({
            'success': True,
            'data': {
                'basic_stats': {
                    'map_views': map_views,
                    'chart_generations': chart_generations,
                    'analysis_count': analysis_count,
                    'user_analysis_count': user_analysis_count
                },
                'analysis_type_stats': [{'type': t[0], 'count': t[1]} for t in analysis_type_stats],
                'recent_analysis': [{
                    'id': a.id,
                    'type': a.analysis_type,
                    'dataset_name': a.dataset_name,
                    'status': a.status,
                    'created_at': a.created_at.isoformat() if a.created_at else None,
                    'user': a.user.username if a.user else None
                } for a in recent_analysis]
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取可视化管理统计失败: {str(e)}'
        }), 500


@bp.route('/admin/users', methods=['POST'])
@login_required
def admin_add_user():
    """添加新用户 - 仅管理员可访问"""
    if not current_user.is_admin():
        return jsonify({'error': '权限不足'}), 403

    data = request.get_json()

    # 验证必需字段
    required_fields = ['username', 'email', 'password', 'role']
    for field in required_fields:
        if not data.get(field):
            return jsonify({'error': f'缺少必需字段: {field}'}), 400

    # 检查用户名是否已存在
    if User.query.filter_by(username=data['username']).first():
        return jsonify({'error': '用户名已存在'}), 400

    # 检查邮箱是否已存在
    if User.query.filter_by(email=data['email']).first():
        return jsonify({'error': '邮箱已存在'}), 400

    # 获取角色
    role = Role.query.filter_by(name=data['role']).first()
    if not role:
        return jsonify({'error': '无效的角色'}), 400

    try:
        # 创建新用户
        user = User(
            username=data['username'],
            email=data['email'],
            role=role,
            is_active=True
        )
        user.set_password(data['password'])

        db.session.add(user)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '用户创建成功',
            'data': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role.name
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'创建用户失败: {str(e)}'}), 500


@bp.route('/admin/users/<int:user_id>', methods=['PUT'])
@login_required
def admin_update_user(user_id):
    """更新用户信息 - 仅管理员可访问"""
    if not current_user.is_admin():
        return jsonify({'error': '权限不足'}), 403

    user = User.query.get_or_404(user_id)
    data = request.get_json()

    try:
        # 更新用户信息
        if 'username' in data:
            # 检查用户名是否已被其他用户使用
            existing_user = User.query.filter_by(username=data['username']).first()
            if existing_user and existing_user.id != user_id:
                return jsonify({'error': '用户名已存在'}), 400
            user.username = data['username']

        if 'email' in data:
            # 检查邮箱是否已被其他用户使用
            existing_user = User.query.filter_by(email=data['email']).first()
            if existing_user and existing_user.id != user_id:
                return jsonify({'error': '邮箱已存在'}), 400
            user.email = data['email']

        if 'role' in data:
            role = Role.query.filter_by(name=data['role']).first()
            if not role:
                return jsonify({'error': '无效的角色'}), 400
            user.role = role

        if 'is_active' in data:
            user.is_active = data['is_active']

        if 'password' in data and data['password']:
            user.set_password(data['password'])

        user.updated_at = get_china_time()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '用户更新成功',
            'data': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role.name,
                'is_active': user.is_active
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'更新用户失败: {str(e)}'}), 500


@bp.route('/admin/users/<int:user_id>', methods=['DELETE'])
@login_required
def admin_delete_user(user_id):
    """删除用户 - 仅管理员可访问"""
    if not current_user.is_admin():
        return jsonify({'error': '权限不足'}), 403

    # 不能删除自己
    if user_id == current_user.id:
        return jsonify({'error': '不能删除自己的账户'}), 400

    user = User.query.get_or_404(user_id)

    try:
        db.session.delete(user)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '用户删除成功'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除用户失败: {str(e)}'}), 500






@bp.route('/provinces/<province_name>/years')
def get_province_years(province_name):
    """获取指定省份的可用年份列表"""
    try:
        from app.models import RiskAssessmentProject

        # 查询该省份的所有年份
        years_query = db.session.query(
            RiskAssessmentProject.assessment_year,
            func.count(RiskAssessmentProject.id).label('project_count')
        ).filter(
            RiskAssessmentProject.location == province_name,
            RiskAssessmentProject.assessment_year.isnot(None)
        ).group_by(
            RiskAssessmentProject.assessment_year
        ).order_by(
            RiskAssessmentProject.assessment_year.desc()
        ).all()

        if not years_query:
            return jsonify({
                'success': True,
                'message': f'暂无省份 {province_name} 的年份数据',
                'province': province_name,
                'years': [],
                'count': 0
            })

        # 构建年份列表
        years_list = []
        for year, project_count in years_query:
            years_list.append({
                'year': year,
                'project_count': project_count
            })

        return jsonify({
            'success': True,
            'province': province_name,
            'years': years_list,
            'count': len(years_list)
        })

    except Exception as e:
        logger.error(f"获取省份年份数据失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取省份年份数据失败: {str(e)}'
        }), 500


@bp.route('/province-data/<province_name>')
def get_province_data_check(province_name):
    """获取指定省份的风险评估数据，支持年份筛选，返回地图显示所需的详细数据"""
    try:
        from app.models import RiskAssessmentProject, RiskCalculationResult, RiskAssessmentData

        # 获取年份参数
        year_filter = request.args.get('year', type=int)

        # 查询该省份的风险评估项目
        query = RiskAssessmentProject.query.filter(
            RiskAssessmentProject.location == province_name
        )

        # 如果指定了年份，添加年份筛选
        if year_filter:
            query = query.filter(RiskAssessmentProject.assessment_year == year_filter)

        projects = query.all()

        if not projects:
            message = f'{province_name} 暂无风险评估数据'
            if year_filter:
                message = f'{province_name} {year_filter}年 暂无风险评估数据'
            return jsonify({
                'success': True,
                'province': province_name,
                'year': year_filter,
                'has_data': False,
                'data': [],
                'message': message
            })

        # 获取项目的计算结果，包含地理坐标信息
        project_ids = [p.id for p in projects]
        results = db.session.query(
            RiskCalculationResult,
            RiskAssessmentData,
            RiskAssessmentProject.assessment_year
        ).join(
            RiskAssessmentData, RiskCalculationResult.data_id == RiskAssessmentData.id
        ).join(
            RiskAssessmentProject, RiskCalculationResult.project_id == RiskAssessmentProject.id
        ).filter(RiskCalculationResult.project_id.in_(project_ids)).all()

        if not results:
            message = f'{province_name} 的风险评估项目尚未完成计算'
            if year_filter:
                message = f'{province_name} {year_filter}年 的风险评估项目尚未完成计算'
            return jsonify({
                'success': True,
                'province': province_name,
                'year': year_filter,
                'has_data': False,
                'data': [],
                'message': message
            })

        # 构建详细数据，包含地理坐标
        detailed_data = []
        actual_year = None

        for result, data, assessment_year in results:
            # 记录实际的年份
            if actual_year is None:
                actual_year = assessment_year

            # 解析坐标信息
            latitude = None
            longitude = None

            if data.coordinates:
                try:
                    import json
                    coords = json.loads(data.coordinates)
                    if isinstance(coords, dict):
                        latitude = coords.get('latitude') or coords.get('lat')
                        longitude = coords.get('longitude') or coords.get('lng')
                    elif isinstance(coords, list) and len(coords) >= 2:
                        # 假设格式为 [longitude, latitude] 或 [latitude, longitude]
                        longitude, latitude = coords[0], coords[1]
                except (json.JSONDecodeError, ValueError, TypeError):
                    # 如果解析失败，尝试其他格式或跳过
                    pass

            # 如果没有坐标信息，尝试根据城市名称生成模拟坐标（用于演示）
            if not latitude or not longitude:
                # 为一些主要城市提供模拟坐标
                city_coords = {
                    '郑州': [34.7466, 113.6253],
                    '洛阳': [34.6197, 112.4540],
                    '开封': [34.7972, 114.3074],
                    '安阳': [36.1034, 114.3927],
                    '新乡': [35.3026, 113.9268],
                    '焦作': [35.2158, 113.2418],
                    '濮阳': [35.7617, 115.0299],
                    '许昌': [34.0357, 113.8516],
                    '漯河': [33.5818, 114.0166],
                    '三门峡': [34.7732, 111.1815],
                    '南阳': [32.9909, 112.5285],
                    '商丘': [34.4138, 115.6506],
                    '信阳': [32.1285, 114.0918],
                    '周口': [33.6237, 114.6965],
                    '驻马店': [32.9831, 114.0221],
                    '济源': [35.0904, 112.6016],
                    '鹤壁': [35.7554, 114.2944],
                    '平顶山': [33.7453, 113.1929]
                }

                city_name = data.location_name
                if city_name:
                    # 移除"市"字后缀进行匹配
                    city_key = city_name.replace('市', '')
                    if city_key in city_coords:
                        latitude, longitude = city_coords[city_key]

            # 构建地图显示所需的数据格式
            location_data = {
                'location': data.location_name,
                'city_name': data.location_name,
                'risk_level': result.final_risk_level,
                'risk_index': result.risk_index,
                'latitude': float(latitude) if latitude else None,
                'longitude': float(longitude) if longitude else None,
                'elevation': float(data.elevation) if data.elevation else None,
                'rainfall': float(data.annual_precipitation) if data.annual_precipitation else None,
                'drainage_capacity': float(data.drainage_network_density) if data.drainage_network_density else None,
                'assessment_year': assessment_year
            }

            # 只添加有坐标信息的数据
            if location_data['latitude'] and location_data['longitude']:
                detailed_data.append(location_data)

        return jsonify({
            'success': True,
            'province': province_name,
            'year': actual_year,
            'has_data': True,
            'data': detailed_data,
            'count': len(detailed_data),
            'projects': len(projects)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取省份数据失败: {str(e)}'
        }), 500




# 数据处理相关API
@bp.route('/datasets/<int:dataset_id>/clean', methods=['POST'])
@login_required
def clean_dataset(dataset_id):
    """数据清洗"""
    try:
        from app.services.data_processing import DataProcessingService
        from flask_login import current_user

        # 更安全的JSON解析
        try:
            if request.is_json:
                data = request.get_json() or {}
            else:
                # 如果不是JSON请求，使用表单数据或默认值
                data = {}
        except Exception as json_error:
            logger.warning(f"JSON解析失败: {json_error}, 使用默认选项")
            data = {}

        options = data.get('options', {
            'remove_duplicates': True,
            'fix_geometry': True,
            'handle_missing_values': True,
            'quality_assessment': True
        })

        # 检查数据集是否存在
        dataset = Dataset.query.get(dataset_id)
        if not dataset:
            return jsonify({
                'success': False,
                'error': '数据集不存在'
            }), 404

        logger.info(f"开始清洗数据集 {dataset_id}, 选项: {options}")
        result = DataProcessingService.clean_data(dataset_id, options, current_user.id)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        import traceback
        error_msg = f"数据清洗失败: {str(e)}"
        logger.error(f"Clean dataset error: {error_msg}")
        logger.error(f"Traceback: {traceback.format_exc()}")

        return jsonify({
            'success': False,
            'error': error_msg
        }), 500


@bp.route('/datasets/<int:dataset_id>/transform', methods=['POST'])
@login_required
def transform_dataset(dataset_id):
    """投影变换"""
    from app.services.data_processing import DataProcessingService

    data = request.get_json() or {}
    target_crs = data.get('target_crs', 'EPSG:4326')
    options = data.get('options', {
        'accuracy_check': True,
        'preserve_topology': True,
        'update_bounds': True
    })

    result = DataProcessingService.transform_projection(dataset_id, target_crs, options)

    if result['success']:
        return jsonify(result)
    else:
        return jsonify(result), 500


@bp.route('/datasets/<int:dataset_id>/preprocess', methods=['POST'])
@login_required
def preprocess_dataset(dataset_id):
    """数据预处理"""
    from app.services.data_processing import DataProcessingService

    data = request.get_json()
    options = data.get('options', {})

    result = DataProcessingService.preprocess_data(dataset_id, options)

    if result['success']:
        return jsonify(result)
    else:
        return jsonify(result), 500


@bp.route('/datasets/<int:dataset_id>/processing-status')
@login_required
def get_processing_status(dataset_id):
    """获取处理状态"""
    dataset = Dataset.query.get_or_404(dataset_id)

    return jsonify({
        'success': True,
        'data': {
            'id': dataset.id,
            'name': dataset.name,
            'processing_status': dataset.processing_status,
            'processing_log': dataset.processing_log,
            'data_type': dataset.data_type,
            'coordinate_system': dataset.coordinate_system,
            'file_size': dataset.file_size,
            'feature_count': dataset.feature_count
        }
    })


@bp.route('/datasets/batch-process', methods=['POST'])
@login_required
def batch_process_datasets():
    """批量处理数据集"""
    from app.services.data_processing import DataProcessingService

    try:
        data = request.get_json() or {}
        dataset_ids = data.get('dataset_ids', [])
        operations = data.get('operations', ['clean'])
        options = data.get('options', {})

        if not dataset_ids:
            return jsonify({
                'success': False,
                'error': '请选择要处理的数据集'
            }), 400

        if not operations:
            return jsonify({
                'success': False,
                'error': '请选择要执行的操作'
            }), 400

        result = DataProcessingService.batch_process_datasets(dataset_ids, operations, options)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'批量处理失败: {str(e)}'
        }), 500





@bp.route('/datasets/<int:dataset_id>/quality-report')
@login_required
def get_quality_report(dataset_id):
    """获取数据质量报告"""
    from app.services.data_processing import DataProcessingService

    try:
        dataset = Dataset.query.get_or_404(dataset_id)

        # 执行质量评估
        quality_result = DataProcessingService._assess_data_quality(dataset)

        return jsonify({
            'success': True,
            'data': {
                'dataset_id': dataset_id,
                'dataset_name': dataset.name,
                'quality_score': quality_result.get('score', 0),
                'issues': quality_result.get('issues', []),
                'assessment_time': datetime.now().isoformat()
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取质量报告失败: {str(e)}'
        }), 500


@bp.route('/data-processing/history')
@login_required
def get_processing_history():
    """获取数据处理历史记录"""
    from app.services.data_processing import DataProcessingService

    try:
        dataset_id = request.args.get('dataset_id', type=int)
        operation_type = request.args.get('operation_type')
        user_id = request.args.get('user_id', type=int)
        limit = request.args.get('limit', 50, type=int)

        result = DataProcessingService.get_processing_history(
            dataset_id=dataset_id,
            operation_type=operation_type,
            user_id=user_id,
            limit=limit
        )

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取处理历史失败: {str(e)}'
        }), 500


@bp.route('/data-processing/statistics')
@login_required
def get_processing_statistics():
    """获取数据处理统计信息"""
    from app.services.data_processing import DataProcessingService

    try:
        days = request.args.get('days', 30, type=int)

        result = DataProcessingService.get_processing_statistics(days=days)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取处理统计失败: {str(e)}'
        }), 500


@bp.route('/data-processing/configs', methods=['GET'])
@login_required
def get_processing_configs():
    """获取数据处理配置"""
    from app.services.data_processing import DataProcessingService

    try:
        config_name = request.args.get('config_name')
        config_type = request.args.get('config_type')
        data_type = request.args.get('data_type')
        city = request.args.get('city')

        result = DataProcessingService.get_processing_config(
            config_name=config_name,
            config_type=config_type,
            data_type=data_type,
            city=city
        )

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取处理配置失败: {str(e)}'
        }), 500


@bp.route('/data-processing/configs', methods=['POST'])
@login_required
def save_processing_config():
    """保存数据处理配置"""
    from app.services.data_processing import DataProcessingService
    from flask_login import current_user

    try:
        data = request.get_json() or {}

        config_name = data.get('config_name', '').strip()
        config_type = data.get('config_type', '').strip()
        parameters = data.get('parameters', {})
        description = data.get('description', '').strip()
        data_types = data.get('data_types', [])
        cities = data.get('cities', [])
        is_default = data.get('is_default', False)

        if not config_name:
            return jsonify({
                'success': False,
                'error': '配置名称不能为空'
            }), 400

        if not config_type:
            return jsonify({
                'success': False,
                'error': '配置类型不能为空'
            }), 400

        if not parameters:
            return jsonify({
                'success': False,
                'error': '配置参数不能为空'
            }), 400

        result = DataProcessingService.save_processing_config(
            config_name=config_name,
            config_type=config_type,
            parameters=parameters,
            description=description,
            data_types=data_types,
            cities=cities,
            user_id=current_user.id,
            is_default=is_default
        )

        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'保存处理配置失败: {str(e)}'
        }), 500


@bp.route('/data-processing/progress/<int:history_id>')
@login_required
def get_processing_progress(history_id):
    """获取数据处理进度"""
    from app.models import DataProcessingHistory

    try:
        history = DataProcessingHistory.query.get_or_404(history_id)

        return jsonify({
            'success': True,
            'data': {
                'history_id': history.id,
                'dataset_id': history.dataset_id,
                'dataset_name': history.dataset.name if history.dataset else None,
                'operation_type': history.operation_type,
                'operation_name': history.operation_name,
                'status': history.status,
                'progress': history.progress,
                'start_time': history.start_time.isoformat() if history.start_time else None,
                'duration_seconds': history.duration_seconds,
                'processing_log': history.processing_log,
                'error_message': history.error_message
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取处理进度失败: {str(e)}'
        }), 500
