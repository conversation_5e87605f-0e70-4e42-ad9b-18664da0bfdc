{% extends "base.html" %}

{% block title %}数据导入 - GIS城市内涝灾害风险预警系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-upload me-2"></i>数据导入
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">仪表板</a></li>
                <li class="breadcrumb-item active">数据导入</li>
            </ol>
        </nav>
    </div>

    <!-- 导入说明 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>数据导入说明</h5>
                <ul class="mb-0">
                    <li>支持批量选择文件或整个文件夹导入，无数量限制</li>
                    <li>支持任意格式文件：GIS数据、文档、图片、压缩包等所有类型</li>
                    <li>文件将保存到 model_data 文件夹中，按类型智能分类存储</li>
                    <li>系统会自动分析文件类型并提取基本信息</li>
                    <li>无文件大小限制，支持任意深度的文件夹结构</li>
                    <li>未识别类型的文件将归类到"其他"类别中</li>
                    <li><strong>自动导入功能</strong>：点击"自动导入"按钮可扫描并导入所有未处理的土地利用和行政区划数据</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 数据导入表单 -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cloud-upload-alt me-2"></i>上传数据文件
                    </h6>
                </div>
                <div class="card-body">
                    <form id="uploadForm" enctype="multipart/form-data">
                        <!-- 数据集基本信息 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="datasetName" class="form-label">数据集名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="datasetName" name="name" required>
                                <div class="form-text">为您的数据集起一个有意义的名称</div>
                            </div>
                            <div class="col-md-6">
                                <label for="dataType" class="form-label">数据类型</label>
                                <select class="form-select" id="dataType" name="data_type">
                                    <option value="unknown">自动识别</option>
                                    <option value="land_use">土地利用</option>
                                    <option value="rivers">河流水系</option>
                                    <option value="drainage">排水系统</option>
                                    <option value="raster">栅格数据</option>
                                    <option value="dem">数字高程模型</option>
                                    <option value="vector">矢量数据</option>
                                    <option value="geojson">GeoJSON数据</option>
                                    <option value="document">文档资料</option>
                                    <option value="tabular">表格数据</option>
                                    <option value="archive">压缩文件</option>
                                    <option value="other">其他类型</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">数据描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                placeholder="请描述数据的来源、用途、特点等信息..."></textarea>
                        </div>

                        <!-- 文件上传区域 -->
                        <div class="mb-4">
                            <label class="form-label">选择文件 <span class="text-danger">*</span></label>

                            <!-- 文件选择按钮 -->
                            <div class="d-flex gap-2 mb-3">
                                <button type="button" class="btn btn-outline-primary" onclick="selectFiles()">
                                    <i class="fas fa-file me-1"></i>选择文件
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="selectFolder()">
                                    <i class="fas fa-folder me-1"></i>选择文件夹
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="clearFiles()">
                                    <i class="fas fa-trash me-1"></i>清空
                                </button>
                            </div>

                            <!-- 隐藏的文件输入 - 移除所有格式限制 -->
                            <input type="file" id="fileInput" name="files" multiple style="display: none;">
                            <input type="file" id="folderInput" name="files" multiple webkitdirectory style="display: none;">

                            <!-- 拖拽上传区域 -->
                            <div id="dropZone" class="border border-2 border-dashed rounded p-4 text-center">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <p class="mb-2">将文件拖拽到此处，或点击上方按钮选择文件</p>
                                <p class="text-muted small">支持任意格式、任意大小、任意数量的文件和文件夹导入</p>
                                <div class="mt-3">
                                    <small class="text-success">
                                        <i class="fas fa-check-circle me-1"></i>无文件大小限制
                                    </small>
                                    <small class="text-success ms-3">
                                        <i class="fas fa-check-circle me-1"></i>无文件格式限制
                                    </small>
                                    <small class="text-success ms-3">
                                        <i class="fas fa-check-circle me-1"></i>无数量限制
                                    </small>
                                </div>
                            </div>

                            <!-- 文件列表 -->
                            <div id="fileList" class="mt-3" style="display: none;">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">已选择文件</h6>
                                    <div>
                                        <span class="badge bg-primary me-2">
                                            <span id="fileCount">0</span> 个文件
                                        </span>
                                        <span class="badge bg-info me-2">
                                            总大小: <span id="totalSize">0 B</span>
                                        </span>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleFileDetails()">
                                            <i class="fas fa-eye me-1"></i>详情
                                        </button>
                                    </div>
                                </div>

                                <!-- 文件类型统计 -->
                                <div id="fileTypeStats" class="mb-3" style="display: none;">
                                    <div class="row g-2" id="typeStatsContainer">
                                        <!-- 类型统计将在这里显示 -->
                                    </div>
                                </div>

                                <div class="list-group" id="fileItems" style="max-height: 300px; overflow-y: auto;">
                                    <!-- 文件项将在这里动态添加 -->
                                </div>
                            </div>
                        </div>

                        <!-- 上传按钮 -->
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="clearFiles()">
                                <i class="fas fa-times me-1"></i>清空文件
                            </button>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary" id="uploadBtn">
                                    <i class="fas fa-upload me-1"></i>开始上传
                                </button>
                            </div>
                        </div>

                        <!-- 简化的上传进度提示 -->
                        <div id="uploadProgress" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <i class="fas fa-spinner fa-spin me-2"></i>正在上传文件，请稍候...
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 侧边栏 - 最近上传和统计 -->
        <div class="col-lg-4">
            <!-- 数据集统计 -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>数据统计
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-1" id="totalDatasets">-</h4>
                                <small class="text-muted">总数据集</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success mb-1" id="userDatasets">-</h4>
                            <small class="text-muted">我的数据集</small>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <small class="text-muted">总存储空间：<span id="totalStorage">-</span></small>
                    </div>
                </div>
            </div>

            <!-- 支持的文件格式 -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-file-alt me-2"></i>支持的文件格式
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary">矢量数据</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-file me-1"></i>Shapefile (.shp, .dbf, .shx, .prj)</li>
                                <li><i class="fas fa-file me-1"></i>GeoJSON (.geojson)</li>
                                <li><i class="fas fa-file me-1"></i>KML/KMZ (.kml, .kmz)</li>
                            </ul>
                            
                            <h6 class="text-success mt-3">栅格数据</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-file me-1"></i>GeoTIFF (.tif, .tiff)</li>
                                <li><i class="fas fa-file me-1"></i>数字高程模型 (DEM)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 上传进度模态框 -->
<div class="modal fade" id="uploadProgressModal" tabindex="-1" aria-labelledby="uploadProgressModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadProgressModalLabel">
                    <i class="fas fa-upload me-2"></i>数据上传进度
                </h5>
                <!-- 不显示关闭按钮，防止用户中断上传 -->
            </div>
            <div class="modal-body">
                <!-- 总体进度 -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">总体进度</h6>
                        <span id="modalUploadStatus" class="badge bg-info">准备中...</span>
                    </div>
                    <div class="progress mb-2" style="height: 20px;">
                        <div id="modalProgressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%">0%</div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <small class="text-muted" id="modalUploadDetails">正在准备上传...</small>
                        <small class="text-muted" id="modalUploadSpeed"></small>
                    </div>
                </div>

                <!-- 详细处理步骤 -->
                <div class="mb-3">
                    <h6 class="mb-2">
                        <i class="fas fa-list me-2"></i>处理步骤
                    </h6>
                    <div id="processingSteps" class="border rounded p-3" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; font-family: 'Courier New', monospace; font-size: 0.85em;">
                        <div class="text-muted">等待开始处理...</div>
                    </div>
                </div>

                <!-- 当前处理的文件 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h6 class="card-title text-primary">当前文件</h6>
                                <p class="card-text" id="currentFileName">-</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h6 class="card-title text-success">已完成</h6>
                                <p class="card-text" id="completedCount">0 / 0</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="modalCloseBtn" style="display: none;" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 加载统计信息
    loadStats();
    
    // 设置拖拽上传
    setupDragAndDrop();
});

// 选择文件
function selectFiles() {
    document.getElementById('fileInput').click();
}

// 选择文件夹
function selectFolder() {
    document.getElementById('folderInput').click();
}

// 批量选择文件夹
function selectMultipleFolders() {
    // 创建临时的文件夹输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.webkitdirectory = true;
    input.style.display = 'none';

    input.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        // 合并现有文件和新选择的文件
        const existingFiles = window.selectedFiles || [];
        const allFiles = [...existingFiles, ...files];
        displayFiles(allFiles);
        document.body.removeChild(input);
    });

    document.body.appendChild(input);
    input.click();
}

// 文件选择事件
document.getElementById('fileInput').addEventListener('change', handleFileSelect);
document.getElementById('folderInput').addEventListener('change', handleFileSelect);

function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    displayFiles(files);
}

// 显示选择的文件
function displayFiles(files) {
    const fileList = document.getElementById('fileList');
    const fileItems = document.getElementById('fileItems');
    const fileCount = document.getElementById('fileCount');
    const totalSize = document.getElementById('totalSize');

    if (files.length === 0) {
        fileList.style.display = 'none';
        return;
    }

    fileList.style.display = 'block';
    fileItems.innerHTML = '';

    let totalBytes = 0;
    const fileTypes = {};

    files.forEach((file, index) => {
        totalBytes += file.size;

        // 统计文件类型
        const ext = file.name.split('.').pop().toLowerCase();
        const fileType = getFileTypeFromExtension(ext);
        fileTypes[fileType] = (fileTypes[fileType] || 0) + 1;

        const item = document.createElement('div');
        item.className = 'list-group-item d-flex justify-content-between align-items-center';

        // 获取文件图标
        const icon = getFileIcon(ext);

        item.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="${icon} me-2 text-primary"></i>
                <div>
                    <div>${file.name}</div>
                    <small class="text-muted">
                        ${formatFileSize(file.size)} • ${fileType}
                        ${file.webkitRelativePath ? ` • ${file.webkitRelativePath}` : ''}
                    </small>
                </div>
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})" title="移除文件">
                <i class="fas fa-times"></i>
            </button>
        `;
        fileItems.appendChild(item);
    });

    fileCount.textContent = files.length;
    totalSize.textContent = formatFileSize(totalBytes);

    // 更新文件类型统计
    updateFileTypeStats(fileTypes);

    // 存储文件列表供后续使用
    window.selectedFiles = files;
}

// 移除文件
function removeFile(index) {
    if (window.selectedFiles) {
        const files = Array.from(window.selectedFiles);
        files.splice(index, 1);
        displayFiles(files);
        
        // 更新文件输入
        const dt = new DataTransfer();
        files.forEach(file => dt.items.add(file));
        document.getElementById('fileInput').files = dt.files;
    }
}

// 清空文件
function clearFiles() {
    document.getElementById('fileInput').value = '';
    document.getElementById('folderInput').value = '';
    document.getElementById('fileList').style.display = 'none';
    window.selectedFiles = [];
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 根据文件扩展名获取文件类型
function getFileTypeFromExtension(ext) {
    const typeMap = {
        // 矢量数据
        'shp': '矢量数据', 'geojson': 'GeoJSON', 'kml': '矢量数据', 'kmz': '矢量数据',
        'gpx': '矢量数据', 'gml': '矢量数据',
        // 栅格数据
        'tif': '栅格数据', 'tiff': '栅格数据', 'img': '栅格数据', 'jp2': '栅格数据',
        'png': '图像', 'jpg': '图像', 'jpeg': '图像',
        // DEM数据
        'dem': 'DEM数据', 'asc': 'DEM数据', 'xyz': 'DEM数据',
        // 数据库文件
        'dbf': '数据库', 'shx': '索引文件', 'prj': '投影文件', 'cpg': '编码文件',
        // 文档
        'txt': '文本文档', 'xml': 'XML文档', 'json': 'JSON数据', 'csv': 'CSV表格',
        'xlsx': 'Excel表格', 'xls': 'Excel表格', 'pdf': 'PDF文档',
        'doc': 'Word文档', 'docx': 'Word文档',
        // 压缩文件
        'zip': '压缩文件', 'rar': '压缩文件', '7z': '压缩文件'
    };
    return typeMap[ext] || '其他文件';
}

// 根据文件扩展名获取图标
function getFileIcon(ext) {
    const iconMap = {
        // 矢量数据
        'shp': 'fas fa-map', 'geojson': 'fas fa-code', 'kml': 'fas fa-map-marker-alt',
        'kmz': 'fas fa-map-marker-alt', 'gpx': 'fas fa-route', 'gml': 'fas fa-map',
        // 栅格数据
        'tif': 'fas fa-image', 'tiff': 'fas fa-image', 'img': 'fas fa-image',
        'jp2': 'fas fa-image', 'png': 'fas fa-image', 'jpg': 'fas fa-image', 'jpeg': 'fas fa-image',
        // DEM数据
        'dem': 'fas fa-mountain', 'asc': 'fas fa-mountain', 'xyz': 'fas fa-mountain',
        // 数据库文件
        'dbf': 'fas fa-database', 'shx': 'fas fa-list', 'prj': 'fas fa-globe', 'cpg': 'fas fa-font',
        // 文档
        'txt': 'fas fa-file-alt', 'xml': 'fas fa-code', 'json': 'fas fa-code',
        'csv': 'fas fa-table', 'xlsx': 'fas fa-file-excel', 'xls': 'fas fa-file-excel',
        'pdf': 'fas fa-file-pdf', 'doc': 'fas fa-file-word', 'docx': 'fas fa-file-word',
        // 压缩文件
        'zip': 'fas fa-file-archive', 'rar': 'fas fa-file-archive', '7z': 'fas fa-file-archive'
    };
    return iconMap[ext] || 'fas fa-file';
}

// 更新文件类型统计
function updateFileTypeStats(fileTypes) {
    const container = document.getElementById('typeStatsContainer');
    container.innerHTML = '';

    Object.entries(fileTypes).forEach(([type, count]) => {
        const col = document.createElement('div');
        col.className = 'col-auto';
        col.innerHTML = `
            <span class="badge bg-secondary">
                ${type}: ${count}
            </span>
        `;
        container.appendChild(col);
    });
}

// 切换文件详情显示
function toggleFileDetails() {
    const stats = document.getElementById('fileTypeStats');
    const btn = event.target.closest('button');

    if (stats.style.display === 'none') {
        stats.style.display = 'block';
        btn.innerHTML = '<i class="fas fa-eye-slash me-1"></i>隐藏';
    } else {
        stats.style.display = 'none';
        btn.innerHTML = '<i class="fas fa-eye me-1"></i>详情';
    }
}

// 设置拖拽上传
function setupDragAndDrop() {
    const dropZone = document.getElementById('dropZone');
    
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.classList.add('border-primary', 'bg-light');
    });
    
    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-primary', 'bg-light');
    });
    
    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-primary', 'bg-light');
        
        const files = Array.from(e.dataTransfer.files);
        displayFiles(files);
        
        // 更新文件输入
        const dt = new DataTransfer();
        files.forEach(file => dt.items.add(file));
        document.getElementById('fileInput').files = dt.files;
    });
}

// 表单提交
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData();
    const files = window.selectedFiles || [];

    if (files.length === 0) {
        alert('请选择要上传的文件');
        return;
    }

    // 验证数据集名称
    const datasetName = document.getElementById('datasetName').value.trim();
    if (!datasetName) {
        alert('请输入数据集名称');
        return;
    }

    // 添加文件
    files.forEach(file => {
        formData.append('files', file);
    });

    // 添加其他表单数据
    formData.append('name', datasetName);
    formData.append('description', document.getElementById('description').value);
    formData.append('data_type', document.getElementById('dataType').value);

    // 显示进度模态框
    const uploadBtn = document.getElementById('uploadBtn');
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>上传中...';

    // 显示详细进度模态框
    showUploadProgressModal(files.length);

    const startTime = Date.now();
    let totalSize = files.reduce((sum, file) => sum + file.size, 0);

    // 创建XMLHttpRequest以支持进度监控
    const xhr = new XMLHttpRequest();

    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            updateUploadProgress(percentComplete, e.loaded, e.total, startTime);
        }
    });

    xhr.addEventListener('load', function() {
        try {
            const data = JSON.parse(xhr.responseText);
            if (data.success) {
                // 显示处理完成状态
                updateUploadStatus('success', '处理完成', '✅ 数据上传和处理完成');

                // 显示详细的处理步骤
                console.log('Processing steps received:', data.data.processing_steps);
                if (data.data.processing_steps && data.data.processing_steps.length > 0) {
                    data.data.processing_steps.forEach((step, index) => {
                        console.log(`Step ${index}:`, step);

                        // 解析不同格式的日志信息
                        if (step.includes('INFO:app.services.')) {
                            // 处理详细的服务日志
                            const message = step.split('INFO:app.services.')[1];
                            if (message.includes('类型') && message.includes('(') && message.includes(')') && message.includes(':')) {
                                addProcessingStep('success', '颜色映射', message);
                            } else if (message.includes('TIFF数据信息') || message.includes('唯一值')) {
                                addProcessingStep('info', '数据分析', message);
                            } else if (message.includes('智能分类结果')) {
                                addProcessingStep('info', '智能分类', message);
                            } else if (message.includes('标准化省份名称')) {
                                addProcessingStep('info', '省份标准化', message);
                            } else if (message.includes('成功创建') || message.includes('处理完成')) {
                                addProcessingStep('success', '处理成功', message);
                            } else if (message.includes('开始') || message.includes('正在')) {
                                addProcessingStep('info', '正在处理', message);
                            } else {
                                addProcessingStep('info', '详细信息', message);
                            }
                        } else if (step.startsWith('INFO:')) {
                            const message = step.replace('INFO:', '').trim();
                            if (message.includes('类型') && message.includes('(') && message.includes(')') && message.includes(':')) {
                                addProcessingStep('success', '颜色映射', message);
                            } else if (message.includes('TIFF数据信息') || message.includes('唯一值')) {
                                addProcessingStep('info', '数据分析', message);
                            } else if (message.includes('成功') || message.includes('完成')) {
                                addProcessingStep('success', '处理成功', message);
                            } else if (message.includes('开始') || message.includes('正在')) {
                                addProcessingStep('info', '正在处理', message);
                            } else {
                                addProcessingStep('info', '信息', message);
                            }
                        } else if (step.startsWith('ERROR:')) {
                            const message = step.replace('ERROR:', '').trim();
                            addProcessingStep('error', '处理失败', message);
                        } else if (step.startsWith('WARNING:')) {
                            const message = step.replace('WARNING:', '').trim();
                            addProcessingStep('warning', '警告', message);
                        } else {
                            // 处理其他格式的步骤
                            if (step.includes('✅')) {
                                addProcessingStep('success', '处理成功', step);
                            } else if (step.includes('❌')) {
                                addProcessingStep('error', '处理失败', step);
                            } else if (step.includes('⚠️')) {
                                addProcessingStep('warning', '注意', step);
                            } else if (step.includes('🔄')) {
                                addProcessingStep('info', '正在处理', step);
                            } else if (step.includes('🌱')) {
                                addProcessingStep('info', '检测文件', step);
                            } else if (step.includes('🎉')) {
                                addProcessingStep('success', '处理完成', step);
                            } else {
                                addProcessingStep('info', '信息', step);
                            }
                        }
                    });
                } else {
                    console.log('No processing steps found');
                    if (data.data.processing_log) {
                        addProcessingStep('success', '处理完成', data.data.processing_log);
                    }
                }

                // 更新完成计数
                document.getElementById('completedCount').textContent = `${data.data.files_count} / ${data.data.files_count}`;

                // 显示最终结果
                addProcessingStep('success', '上传完成', `✅ 成功上传并处理了 ${data.data.files_count} 个文件`);

                // 启用关闭按钮
                document.getElementById('modalCloseBtn').style.display = 'inline-block';

                // 重置表单和重新加载统计信息
                setTimeout(() => {
                    document.getElementById('uploadForm').reset();
                    clearFiles();
                    loadStats();
                }, 1000);
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            updateUploadStatus('error', '处理失败', error.message);
            addProcessingStep('error', '处理失败', error.message);
            document.getElementById('modalCloseBtn').style.display = 'inline-block';
        }
    });

    xhr.addEventListener('error', function() {
        updateUploadStatus('error', '上传失败', '网络连接错误，请检查网络后重试');
        addProcessingStep('error', '网络错误', '上传过程中发生网络错误');
        document.getElementById('modalCloseBtn').style.display = 'inline-block';
    });

    xhr.addEventListener('loadend', function() {
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="fas fa-upload me-1"></i>开始上传';
    });

    xhr.open('POST', '/api/datasets/upload');
    xhr.send(formData);
});

// 加载统计信息
function loadStats() {
    fetch('/api/datasets/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('totalDatasets').textContent = data.data.total_datasets;
                document.getElementById('userDatasets').textContent = data.data.user_datasets;
                document.getElementById('totalStorage').textContent = formatFileSize(data.data.total_size);
            }
        })
        .catch(error => {
            console.error('加载统计信息失败:', error);
        });
}





// 显示上传进度模态框
function showUploadProgressModal(fileCount) {
    // 重置模态框状态
    document.getElementById('modalUploadStatus').textContent = '准备上传';
    document.getElementById('modalUploadStatus').className = 'badge bg-info';
    document.getElementById('modalProgressBar').style.width = '0%';
    document.getElementById('modalProgressBar').textContent = '0%';
    document.getElementById('modalUploadDetails').textContent = `准备上传 ${fileCount} 个文件...`;
    document.getElementById('modalUploadSpeed').textContent = '';
    document.getElementById('currentFileName').textContent = '-';
    document.getElementById('completedCount').textContent = `0 / ${fileCount}`;

    // 清空处理步骤
    const stepsContainer = document.getElementById('processingSteps');
    stepsContainer.innerHTML = '<div class="text-muted">正在准备上传...</div>';

    // 隐藏关闭按钮
    document.getElementById('modalCloseBtn').style.display = 'none';

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('uploadProgressModal'));
    modal.show();

    // 添加初始步骤
    addProcessingStep('info', '开始上传', `检测到 ${fileCount} 个文件，开始上传处理...`);

    // 模拟一些初始处理步骤
    setTimeout(() => {
        addProcessingStep('info', '验证文件', '正在验证文件格式和完整性...');
    }, 500);

    setTimeout(() => {
        addProcessingStep('info', '准备存储', '正在准备数据存储空间...');
    }, 1000);
}

// 更新上传进度
function updateUploadProgress(percentage, loaded, total, startTime) {
    const progressBar = document.getElementById('modalProgressBar');
    const details = document.getElementById('modalUploadDetails');
    const speed = document.getElementById('modalUploadSpeed');

    // 更新进度条
    progressBar.style.width = percentage + '%';
    progressBar.textContent = Math.round(percentage) + '%';

    // 更新详细信息
    details.textContent = `已上传 ${formatFileSize(loaded)} / ${formatFileSize(total)}`;

    // 计算上传速度
    const elapsed = (Date.now() - startTime) / 1000;
    const uploadSpeed = loaded / elapsed;
    speed.textContent = `${formatFileSize(uploadSpeed)}/s`;

    // 更新状态
    if (percentage >= 100) {
        updateUploadStatus('warning', '上传完成', '正在处理文件...');
        addProcessingStep('success', '上传完成', '文件上传完成，开始处理数据...');
    } else {
        updateUploadStatus('info', '上传中', `上传进度 ${Math.round(percentage)}%`);
    }
}

// 更新上传状态
function updateUploadStatus(type, status, details) {
    const statusBadge = document.getElementById('modalUploadStatus');
    const detailsElement = document.getElementById('modalUploadDetails');

    // 更新状态徽章
    statusBadge.textContent = status;
    statusBadge.className = `badge bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'info'}`;

    // 更新详细信息
    detailsElement.textContent = details;
}

// 添加处理步骤
function addProcessingStep(type, title, message) {
    const stepsContainer = document.getElementById('processingSteps');
    const timestamp = new Date().toLocaleTimeString();

    // 如果是第一个步骤，清空默认内容
    if (stepsContainer.children.length === 1 && stepsContainer.children[0].textContent.includes('等待开始处理')) {
        stepsContainer.innerHTML = '';
    }

    // 创建步骤元素
    const stepDiv = document.createElement('div');
    stepDiv.className = `mb-2 p-2 rounded ${type === 'success' ? 'bg-success bg-opacity-10 border border-success' : type === 'error' ? 'bg-danger bg-opacity-10 border border-danger' : type === 'warning' ? 'bg-warning bg-opacity-10 border border-warning' : 'bg-info bg-opacity-10 border border-info'}`;

    // 从消息中提取图标，如果没有则使用默认图标
    let icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
    let cleanMessage = message;

    // 检查消息是否已包含图标
    const iconMatch = message.match(/^([🔄🌱✅❌⚠️🎉📊ℹ️])\s*/);
    if (iconMatch) {
        icon = iconMatch[1];
        cleanMessage = message.replace(iconMatch[0], '');
    }

    // 特殊处理技术性信息
    let messageClass = 'text-muted';
    let messageStyle = 'font-size: 0.9em; white-space: pre-line; margin-left: 1.5em;';

    if (title === '颜色映射' || title === '数据分析') {
        messageClass = 'text-dark';
        messageStyle += ' font-family: "Courier New", monospace; background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px;';
    }

    stepDiv.innerHTML = `
        <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
                <div class="d-flex align-items-center mb-1">
                    <span class="me-2" style="font-size: 1.1em;">${icon}</span>
                    <strong class="text-${type === 'success' ? 'success' : type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'info'}">${title}</strong>
                </div>
                <div class="${messageClass}" style="${messageStyle}">${cleanMessage}</div>
            </div>
            <small class="text-muted ms-2" style="font-size: 0.8em;">${timestamp}</small>
        </div>
    `;

    // 添加到容器
    stepsContainer.appendChild(stepDiv);

    // 滚动到底部
    stepsContainer.scrollTop = stepsContainer.scrollHeight;

    // 如果是当前正在处理的文件，更新当前文件显示
    if (message.includes('处理土地利用文件') && message.includes(':')) {
        const fileName = message.split(':')[1]?.trim();
        if (fileName) {
            document.getElementById('currentFileName').textContent = fileName;
        }
    }
}


</script>
{% endblock %}
