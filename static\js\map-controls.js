/**
 * 地图控件库 - 指北针和比例尺控件
 * 用于为Leaflet地图添加导航辅助控件
 */

/**
 * 指北针控件类
 * 创建一个简单的北向指示器控件
 */
L.Control.Compass = L.Control.extend({
    options: {
        position: 'topright',
        title: '指北针 - 北向指示器'
    },

    onAdd: function(map) {
        // 创建控件容器
        const container = L.DomUtil.create('div', 'leaflet-control-compass');
        
        // 设置容器属性
        container.title = this.options.title;
        container.setAttribute('aria-label', this.options.title);
        
        // 创建指北针内容
        container.innerHTML = `
            <div class="compass-container">
                <div class="compass-needle">
                    <div class="compass-north">N</div>
                    <div class="compass-arrow">↑</div>
                </div>
            </div>
        `;

        // 阻止地图事件冒泡
        L.DomEvent.disableClickPropagation(container);
        L.DomEvent.disableScrollPropagation(container);

        return container;
    },

    onRemove: function(map) {
        // 清理工作（如果需要）
    }
});

// 添加到L.control命名空间
L.control.compass = function(options) {
    return new L.Control.Compass(options);
};

/**
 * 比例尺控件配置函数
 * 返回配置好的比例尺控件
 */
function createScaleControl(options = {}) {
    const defaultOptions = {
        position: 'bottomleft',
        maxWidth: 150,
        metric: true,      // 显示公制单位（米/千米）
        imperial: false,   // 不显示英制单位
        updateWhenIdle: false  // 实时更新，不等待地图停止移动
    };

    const scaleControl = L.control.scale(Object.assign(defaultOptions, options));

    // 添加自定义事件监听
    scaleControl.onAdd = function(map) {
        const container = L.Control.Scale.prototype.onAdd.call(this, map);

        // 添加自定义类名用于样式控制
        L.DomUtil.addClass(container, 'custom-scale-control');

        // 监听缩放事件，确保比例尺及时更新
        map.on('zoomend moveend', () => {
            this._update();
        });

        return container;
    };

    return scaleControl;
}

/**
 * 高级比例尺控件 - 带有更多信息显示
 */
L.Control.AdvancedScale = L.Control.extend({
    options: {
        position: 'bottomleft',
        maxWidth: 150,
        showZoomLevel: true,
        showCoordinates: false
    },

    onAdd: function(map) {
        this._map = map;

        const container = L.DomUtil.create('div', 'leaflet-control-advanced-scale');

        // 创建比例尺容器
        this._scaleContainer = L.DomUtil.create('div', 'advanced-scale-bar', container);

        // 创建信息显示容器
        if (this.options.showZoomLevel || this.options.showCoordinates) {
            this._infoContainer = L.DomUtil.create('div', 'advanced-scale-info', container);
        }

        // 阻止地图事件冒泡
        L.DomEvent.disableClickPropagation(container);
        L.DomEvent.disableScrollPropagation(container);

        // 绑定事件
        map.on('zoomend moveend', this._update, this);

        // 初始更新
        this._update();

        return container;
    },

    onRemove: function(map) {
        map.off('zoomend moveend', this._update, this);
    },

    _update: function() {
        const map = this._map;
        const zoom = map.getZoom();
        const center = map.getCenter();

        // 更新比例尺
        this._updateScale();

        // 更新附加信息
        if (this._infoContainer) {
            let infoText = '';

            if (this.options.showZoomLevel) {
                infoText += `缩放级别: ${zoom}`;
            }

            if (this.options.showCoordinates) {
                if (infoText) infoText += ' | ';
                infoText += `坐标: ${center.lat.toFixed(4)}, ${center.lng.toFixed(4)}`;
            }

            this._infoContainer.innerHTML = infoText;
        }
    },

    _updateScale: function() {
        const map = this._map;
        const maxMeters = this._getMaxMeters();

        if (maxMeters) {
            this._updateMetric(maxMeters);
        }
    },

    _getMaxMeters: function() {
        const map = this._map;
        const crs = map.options.crs;
        const y = map.getSize().y / 2;

        const maxPixels = this.options.maxWidth;
        const distance = crs.distance(
            crs.pointToLatLng(L.point(0, y), map.getZoom()),
            crs.pointToLatLng(L.point(maxPixels, y), map.getZoom())
        );

        return distance;
    },

    _updateMetric: function(maxMeters) {
        const meters = this._getRoundNum(maxMeters);
        const label = meters < 1000 ? meters + ' 米' : (meters / 1000) + ' 千米';
        const ratio = meters / maxMeters;

        this._scaleContainer.style.width = Math.round(this.options.maxWidth * ratio) + 'px';
        this._scaleContainer.innerHTML = label;
    },

    _getRoundNum: function(num) {
        const pow10 = Math.pow(10, (Math.floor(num) + '').length - 1);
        let d = num / pow10;

        d = d >= 10 ? 10 :
            d >= 5 ? 5 :
            d >= 3 ? 3 :
            d >= 2 ? 2 : 1;

        return pow10 * d;
    }
});

// 添加到L.control命名空间
L.control.advancedScale = function(options) {
    return new L.Control.AdvancedScale(options);
};

/**
 * 地图控件初始化函数
 * 为地图添加指北针和比例尺控件
 */
function initMapControls(map, options = {}) {
    if (!map) {
        console.error('地图实例不存在，无法添加控件');
        return;
    }

    const settings = {
        compass: true,
        scale: true,
        compassPosition: 'topright',
        scalePosition: 'bottomleft',
        ...options
    };

    // 添加指北针控件
    if (settings.compass) {
        const compass = L.control.compass({
            position: settings.compassPosition
        });
        compass.addTo(map);
        console.log('指北针控件已添加');
    }

    // 添加比例尺控件
    if (settings.scale) {
        const scale = createScaleControl({
            position: settings.scalePosition
        });
        scale.addTo(map);
        console.log('比例尺控件已添加');
    }
}

/**
 * 移除地图控件
 */
function removeMapControls(map) {
    if (!map) return;

    // 移除所有自定义控件
    map.eachLayer(function(layer) {
        if (layer instanceof L.Control) {
            map.removeControl(layer);
        }
    });
}

// 导出函数供全局使用
window.initMapControls = initMapControls;
window.removeMapControls = removeMapControls;
window.createScaleControl = createScaleControl;
