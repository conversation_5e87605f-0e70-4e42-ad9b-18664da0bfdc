{% extends "base.html" %}

{% block title %}存储管理 - GIS城市内涝灾害风险预警系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-hdd me-2"></i>存储管理
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.dashboard') }}">仪表板</a></li>
                <li class="breadcrumb-item active">存储管理</li>
            </ol>
        </nav>
    </div>


        <!-- 数据集列表 -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-database me-2"></i>数据集列表
                    </h6>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control form-control-sm" id="datasetSearch"
                               placeholder="搜索数据集..." style="width: 200px;">
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshDatasetList()">
                            <i class="fas fa-sync-alt me-1"></i>刷新
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div style="max-height: 400px; overflow-y: auto;">
                        <div id="datasetList">
                            <div class="text-center text-muted p-4">
                                <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                                <p>加载数据集列表中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    loadStorageStats();
    loadStorageStructure();
    loadDatasetList();

    // 搜索功能
    $('#datasetSearch').on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        filterDatasetList(searchTerm);
    });
});
// 加载存储统计
function loadStorageStats() {
    fetch('/api/storage/stats')
      .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStorageStats(data.data);
                updateFolderStatsTable(data.data.folder_stats);
            } else {
                console.error('加载存储统计失败:', data.error);
            }
        })
        .catch(error => {
            console.error('加载存储统计失败:', error);
        });
}


// 加载数据集列表
function loadDatasetList() {
    fetch('/api/datasets')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDatasetList(data.data);
            } else {
                document.getElementById('datasetList').innerHTML =
                    '<div class="text-center text-danger p-4"><p>加载失败: ' + data.error + '</p></div>';
            }
        })
        .catch(error => {
            console.error('加载数据集列表失败:', error);
            document.getElementById('datasetList').innerHTML =
                '<div class="text-center text-danger p-4"><p>加载失败: ' + error.message + '</p></div>';
        });
}

// 显示数据集列表
function displayDatasetList(datasets) {
    const container = document.getElementById('datasetList');

    if (!datasets || datasets.length === 0) {
        container.innerHTML = '<div class="text-center text-muted p-4"><p>暂无数据集</p></div>';
        return;
    }

    const isAdmin = {{ 'true' if current_user.is_admin() else 'false' }};
    let html = '';
    datasets.forEach(dataset => {
        const sizeDisplay = dataset.file_size ? formatFileSize(dataset.file_size) : '-';
        const createdAt = new Date(dataset.created_at).toLocaleDateString('zh-CN');

        html += `
            <div class="dataset-item border-bottom p-3" data-name="${dataset.name.toLowerCase()}" data-type="${dataset.data_type.toLowerCase()}">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">
                            <i class="fas ${dataset.is_raster ? 'fa-image' : 'fa-vector-square'} me-2 text-primary"></i>
                            ${dataset.name}
                        </h6>
                        <p class="text-muted small mb-2">${dataset.description || '无描述'}</p>
                        <div class="row text-sm">
                            <div class="col-6">
                                <small class="text-muted">类型: ${dataset.data_type}</small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">大小: ${sizeDisplay}</small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">创建者: ${dataset.created_by}</small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">创建时间: ${createdAt}</small>
                            </div>
                        </div>
                    </div>
                    <div class="ms-3 d-flex gap-1">
                        <button class="btn btn-sm btn-outline-primary" onclick="viewDataset(${dataset.id})" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${isAdmin ? `
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteDataset(${dataset.id}, '${dataset.name}')" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 过滤数据集列表
function filterDatasetList(searchTerm) {
    const items = document.querySelectorAll('.dataset-item');
    items.forEach(item => {
        const name = item.getAttribute('data-name');
        const type = item.getAttribute('data-type');
        const visible = name.includes(searchTerm) || type.includes(searchTerm);
        item.style.display = visible ? 'block' : 'none';
    });
}

// 刷新数据集列表
function refreshDatasetList() {
    document.getElementById('datasetList').innerHTML =
        '<div class="text-center text-muted p-4"><i class="fas fa-spinner fa-spin fa-2x mb-3"></i><p>刷新中...</p></div>';
    loadDatasetList();
}

// 查看数据集详情
function viewDataset(datasetId) {
    // 获取数据集详细信息
    fetch(`/api/datasets/${datasetId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showDatasetModal(data.data);
            } else {
                alert('获取数据集详情失败: ' + data.error);
            }
        })
        .catch(error => {
            alert('获取数据集详情失败: ' + error.message);
        });
}

// 显示数据集详情模态框
function showDatasetModal(dataset) {
    const sizeDisplay = dataset.file_size ? formatFileSize(dataset.file_size) : '-';
    const createdAt = dataset.created_at ? new Date(dataset.created_at).toLocaleString('zh-CN') : '-';
    const updatedAt = dataset.updated_at ? new Date(dataset.updated_at).toLocaleString('zh-CN') : '-';

    const modalHtml = `
        <div class="modal fade" id="datasetModal" tabindex="-1" aria-labelledby="datasetModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="datasetModalLabel">
                            <i class="fas ${dataset.is_raster ? 'fa-image' : 'fa-vector-square'} me-2"></i>
                            ${dataset.name}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">基本信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>数据类型:</strong></td>
                                        <td>${dataset.data_type}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>文件大小:</strong></td>
                                        <td>${sizeDisplay}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>要素数量:</strong></td>
                                        <td>${dataset.feature_count || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>坐标系统:</strong></td>
                                        <td>${dataset.coordinate_system || '-'}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">创建信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>创建者:</strong></td>
                                        <td>${dataset.created_by}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>创建时间:</strong></td>
                                        <td>${createdAt}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>更新时间:</strong></td>
                                        <td>${updatedAt}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>文件路径:</strong></td>
                                        <td><small class="text-muted">${dataset.file_path}</small></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        ${dataset.description ? `
                            <div class="mt-3">
                                <h6 class="text-primary mb-2">描述</h6>
                                <p class="text-muted">${dataset.description}</p>
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-info me-2" onclick="classifyDataset(${dataset.id}, '${dataset.name}')">
                            <i class="fas fa-brain me-1"></i>智能分类
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('datasetModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('datasetModal'));
    modal.show();

    // 模态框关闭后移除DOM元素
    document.getElementById('datasetModal').addEventListener('hidden.bs.modal', function () {
        this.remove();
    });
}



// 删除数据集
function deleteDataset(datasetId, datasetName) {
    if (!confirm(`确定要删除数据集 "${datasetName}" 吗？此操作不可撤销。`)) {
        return;
    }

    fetch(`/api/datasets/${datasetId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('数据集删除成功');
            loadDatasetList(); // 刷新列表
            loadStorageStats(); // 刷新统计
        } else {
            alert('删除失败: ' + data.error);
        }
    })
    .catch(error => {
        alert('删除失败: ' + error.message);
    });
}

// 加载存储结构
function loadStorageStructure() {
    fetch('/api/storage/structure')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayStorageStructure(data.data);
            } else {
                document.getElementById('storageStructure').innerHTML = 
                    '<div class="text-center text-danger"><p>加载失败: ' + data.error + '</p></div>';
            }
        })
        .catch(error => {
            console.error('加载存储结构失败:', error);
            document.getElementById('storageStructure').innerHTML = 
                '<div class="text-center text-danger"><p>加载失败: ' + error.message + '</p></div>';
        });
}

// 显示存储结构
function displayStorageStructure(structure) {
    const container = document.getElementById('storageStructure');
    container.innerHTML = '';
    
    function createTreeNode(node, level = 0) {
        const div = document.createElement('div');
        div.style.marginLeft = (level * 20) + 'px';
        div.className = 'mb-1';
        
        if (node.type === 'folder') {
            div.innerHTML = `
                <i class="fas fa-folder text-warning me-2"></i>
                <span class="fw-bold">${node.name}</span>
                ${node.children && node.children.length > 0 ? 
                    `<small class="text-muted ms-2">(${node.children.length} 项)</small>` : ''}
            `;
        } else if (node.type === 'file') {
            div.innerHTML = `
                <i class="fas fa-file text-info me-2"></i>
                <span>${node.name}</span>
                <small class="text-muted ms-2">(${node.size_mb} MB)</small>
            `;
        } else if (node.type === 'more') {
            div.innerHTML = `
                <i class="fas fa-ellipsis-h text-muted me-2"></i>
                <span class="text-muted">${node.name}</span>
            `;
        }
        
        container.appendChild(div);
        
        if (node.children && level < 2) {
            node.children.forEach(child => createTreeNode(child, level + 1));
        }
    }
    
    createTreeNode(structure);
}

// 刷新存储结构
function refreshStorageStructure() {
    document.getElementById('storageStructure').innerHTML =
        '<div class="text-center text-muted"><i class="fas fa-spinner fa-spin fa-2x mb-3"></i><p>刷新中...</p></div>';
    loadStorageStructure();
}

// 智能分类数据集
function classifyDataset(datasetId, datasetName) {
    // 显示加载状态
    const loadingHtml = `
        <div class="modal fade" id="classifyModal" tabindex="-1" aria-labelledby="classifyModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="classifyModalLabel">
                            <i class="fas fa-brain me-2"></i>智能分类分析
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">分析中...</span>
                        </div>
                        <h5>正在分析数据集: ${datasetName}</h5>
                        <p class="text-muted">这可能需要几秒钟时间，请稍候...</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的分类模态框
    const existingModal = document.getElementById('classifyModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加加载模态框
    document.body.insertAdjacentHTML('beforeend', loadingHtml);
    const loadingModal = new bootstrap.Modal(document.getElementById('classifyModal'));
    loadingModal.show();

    // 发送分类请求
    fetch(`/api/datasets/${datasetId}/classify`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();

        if (data.success) {
            showClassificationResults(data.data);
        } else {
            alert('智能分类失败: ' + data.error);
        }
    })
    .catch(error => {
        loadingModal.hide();
        alert('智能分类失败: ' + error.message);
    });
}

// 显示分类结果
function showClassificationResults(results) {
    let resultsHtml = '';

    results.classification_results.forEach(result => {
        if (result.classification) {
            const classification = result.classification.classification;
            const confidence = (classification.confidence * 100).toFixed(1);

            resultsHtml += `
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-file-image me-2"></i>${result.filename}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">分类结果</h6>
                                <ul class="list-unstyled">
                                    <li><strong>省份:</strong> ${classification.province || '未识别'}</li>
                                    <li><strong>数据类型:</strong> ${classification.description || classification.data_type}</li>
                                    <li><strong>置信度:</strong>
                                        <span class="badge ${confidence > 70 ? 'bg-success' : confidence > 50 ? 'bg-warning' : 'bg-danger'}">
                                            ${confidence}%
                                        </span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">分析方法</h6>
                                <div class="mb-2">
                                    ${classification.analysis_methods.map(method => {
                                        const methodNames = {
                                            'filename_analysis': '文件名分析',
                                            'boundary_analysis': '地理边界分析',
                                            'content_analysis': '数据内容分析'
                                        };
                                        return `<span class="badge bg-info me-1">${methodNames[method] || method}</span>`;
                                    }).join('')}
                                </div>
                                ${result.classification.suggested_tags ? `
                                    <h6 class="text-primary mt-3">建议标签</h6>
                                    <div>
                                        ${result.classification.suggested_tags.map(tag =>
                                            `<span class="badge bg-secondary me-1">${tag}</span>`
                                        ).join('')}
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else if (result.error) {
            resultsHtml += `
                <div class="card mb-3 border-danger">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>${result.filename}
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="text-danger mb-0">分析失败: ${result.error}</p>
                    </div>
                </div>
            `;
        }
    });

    const modalHtml = `
        <div class="modal fade" id="classifyResultModal" tabindex="-1" aria-labelledby="classifyResultModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="classifyResultModalLabel">
                            <i class="fas fa-brain me-2"></i>智能分类结果 - ${results.dataset_name}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">分析摘要</h6>
                            <p class="mb-0">
                                总文件数: ${results.summary.total_files} |
                                成功分析: ${results.summary.successful_classifications} |
                                分析失败: ${results.summary.failed_classifications}
                            </p>
                        </div>
                        ${resultsHtml}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="refreshDatasetList()">
                            <i class="fas fa-sync-alt me-1"></i>刷新数据集列表
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的结果模态框
    const existingModal = document.getElementById('classifyResultModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加结果模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const resultModal = new bootstrap.Modal(document.getElementById('classifyResultModal'));
    resultModal.show();

    // 模态框关闭后移除DOM元素
    document.getElementById('classifyResultModal').addEventListener('hidden.bs.modal', function () {
        this.remove();
    });
}


</script>
{% endblock %}
