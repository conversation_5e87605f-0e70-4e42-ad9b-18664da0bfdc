<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/maps/henan.js"></script>

    
</head>
<body >
    <div id="efb0cc156552413a898dd3ab512d99f1" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_efb0cc156552413a898dd3ab512d99f1 = echarts.init(
            document.getElementById('efb0cc156552413a898dd3ab512d99f1'), 'white', {renderer: 'canvas'});
        var option_efb0cc156552413a898dd3ab512d99f1 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "map",
            "name": "\u98ce\u9669\u7b49\u7ea7",
            "label": {
                "show": true,
                "margin": 8,
                "fontSize": 10,
                "valueAnimation": false
            },
            "map": "\u6cb3\u5357",
            "data": [
                {
                    "name": "\u90d1\u5dde\u5e02",
                    "value": 4
                },
                {
                    "name": "\u5f00\u5c01\u5e02",
                    "value": 2
                },
                {
                    "name": "\u6d1b\u9633\u5e02",
                    "value": 2
                },
                {
                    "name": "\u5e73\u9876\u5c71\u5e02",
                    "value": 2
                },
                {
                    "name": "\u5b89\u9633\u5e02",
                    "value": 2
                },
                {
                    "name": "\u9e64\u58c1\u5e02",
                    "value": 2
                },
                {
                    "name": "\u65b0\u4e61\u5e02",
                    "value": 2
                },
                {
                    "name": "\u7126\u4f5c\u5e02",
                    "value": 2
                },
                {
                    "name": "\u6fee\u9633\u5e02",
                    "value": 3
                },
                {
                    "name": "\u8bb8\u660c\u5e02",
                    "value": 3
                },
                {
                    "name": "\u6f2f\u6cb3\u5e02",
                    "value": 3
                },
                {
                    "name": "\u4e09\u95e8\u5ce1\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5357\u9633\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5546\u4e18\u5e02",
                    "value": 3
                },
                {
                    "name": "\u4fe1\u9633\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5468\u53e3\u5e02",
                    "value": 2
                },
                {
                    "name": "\u9a7b\u9a6c\u5e97\u5e02",
                    "value": 2
                },
                {
                    "name": "\u6d4e\u6e90\u5e02",
                    "value": 2
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1.2,
            "zlevel": 0,
            "z": 2,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "mapValueCalculation": "sum",
            "showLegendSymbol": true,
            "emphasis": {},
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u98ce\u9669\u7b49\u7ea7"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}: {c}",
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u6cb3\u5357\u7701\u98ce\u9669\u533a\u5212\u56fe",
            "target": "blank",
            "subtext": "\uff082019\u5e74\uff09",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "fontWeight": "bold",
                "fontSize": 18
            }
        }
    ],
    "visualMap": {
        "show": true,
        "type": "continuous",
        "min": 0,
        "max": 5,
        "text": [
            "\u9ad8\u98ce\u9669",
            "\u4f4e\u98ce\u9669"
        ],
        "inRange": {
            "color": [
                "#d4edda",
                "#28a745",
                "#ffc107",
                "#fd7e14",
                "#dc3545"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "hoverLink": true,
        "orient": "vertical",
        "left": "left",
        "bottom": "20%",
        "padding": 5,
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 140,
        "borderWidth": 0
    }
};
        chart_efb0cc156552413a898dd3ab512d99f1.setOption(option_efb0cc156552413a898dd3ab512d99f1);
    </script>
</body>
</html>
