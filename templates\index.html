{% extends "base.html" %}

{% block title %}首页 - {{ super() }}{% endblock %}

{% block content %}
<!-- 英雄区域 -->
<div class="hero-section bg-primary text-white py-5 mb-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-water me-3"></i>
                    GIS城市内涝灾害风险预警系统
                </h1>
                <p class="lead mb-4">
                    基于GIS技术的智能化城市暴雨内涝灾害预警可视化与评估平台，
                    提供风险评估和可视化展示服务。
                </p>
                <div class="d-flex gap-3">
                    {% if current_user.is_authenticated %}
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-tachometer-alt me-2"></i>进入系统
                        </a>
                    {% else %}
                        <a href="{{ url_for('auth.login') }}" class="btn btn-light btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>立即登录
                        </a>
                        <a href="{{ url_for('auth.register') }}" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-user-plus me-2"></i>免费注册
                        </a>
                    {% endif %}
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="fas fa-map-marked-alt" style="font-size: 12rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 功能特性 -->
<div class="container mb-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="display-5 fw-bold">核心功能</h2>
        </div>
    </div>
    
    <div class="row g-4">
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-database fa-3x text-primary"></i>
                    </div>
                    <h5 class="card-title">数据管理</h5>
                    <p class="card-text">
                        支持多格式空间数据导入导出，提供数据预处理、质量检查和元数据管理功能。
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
                    </div>
                    <h5 class="card-title">风险评估预警</h5>
                    <p class="card-text">
                        多因子风险评估模型，预警阈值管理，历史灾情数据分析和预警信息发布。
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-map fa-3x text-info"></i>
                    </div>
                    <h5 class="card-title">可视化展示</h5>
                    <p class="card-text">
                        地图展示，风险分区可视化和统计图表分析。
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-users fa-3x text-secondary"></i>
                    </div>
                    <h5 class="card-title">用户权限管理</h5>
                    <p class="card-text">
                        多角色用户管理，安全的认证机制，基于权限的功能访问控制。
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 技术优势 -->
<div class="bg-light py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-5 fw-bold">技术优势</h2>
                <p class="lead text-muted">先进的技术架构，可靠的系统性能</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-3 text-center">
                <div class="tech-item">
                    <i class="fab fa-python fa-3x text-primary mb-3"></i>
                    <h5>Python 3.8+</h5>
                    <p class="text-muted">强大的后端处理能力</p>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="tech-item">
                    <i class="fas fa-leaf fa-3x text-success mb-3"></i>
                    <h5>Leaflet地图</h5>
                    <p class="text-muted">轻量级交互式地图</p>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="tech-item">
                    <i class="fas fa-database fa-3x text-info mb-3"></i>
                    <h5>SQLite</h5>
                    <p class="text-muted">数据库支持</p>
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="tech-item">
                    <i class="fas fa-chart-bar fa-3x text-warning mb-3"></i>
                    <h5>ECharts图表</h5>
                    <p class="text-muted">丰富的数据可视化</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.hero-section {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.feature-icon {
    transition: transform 0.3s ease;
}

.card:hover .feature-icon {
    transform: scale(1.1);
}

.tech-item {
    transition: transform 0.3s ease;
}

.tech-item:hover {
    transform: translateY(-5px);
}
</style>
{% endblock %}
