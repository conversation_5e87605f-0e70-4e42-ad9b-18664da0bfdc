{% extends "base.html" %}

{% block title %}计算结果 - {{ project.project_name }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-chart-line text-success me-2"></i>计算结果</h2>
                    <p class="text-muted mb-0">{{ project.project_name }} - 风险评估结果可视化</p>
                </div>
                <div>
                    <a href="{{ url_for('visualization.map_view') }}?province={{ project.location }}" class="btn btn-success me-2" target="_blank">
                        <i class="fas fa-map me-1"></i>查看热力图
                    </a>
                    <a href="{{ url_for('risk_assessment.project_detail', project_id=project.id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回项目
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="totalCount">-</h4>
                            <p class="mb-0">总计算数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calculator fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="highRiskCount">-</h4>
                            <p class="mb-0">高风险数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="avgRiskIndex">-</h4>
                            <p class="mb-0">平均风险指数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-bar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="completionTime">-</h4>
                            <p class="mb-0">完成时间</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="row mb-4">
        <!-- 风险等级分布饼图 -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>风险等级分布</h5>
                </div>
                <div class="card-body">
                    <canvas id="riskLevelChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- 指标权重饼图 -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-weight-hanging me-2"></i>指标权重分布</h5>
                </div>
                <div class="card-body">
                    <canvas id="weightChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 风险指数分布柱状图 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>风险指数分布</h5>
                </div>
                <div class="card-body">
                    <canvas id="riskIndexChart" width="800" height="400"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 风险矩阵 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-table me-2"></i>5×5风险矩阵</h5>
                </div>
                <div class="card-body">
                    <div id="riskMatrixContainer">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2">正在加载风险矩阵...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细结果表格 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>详细计算结果</h5>
                    <button class="btn btn-success btn-sm" onclick="exportResults()">
                        <i class="fas fa-download me-1"></i>导出结果
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="resultsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>数据名称</th>
                                    <th>位置</th>
                                    <th>发生概率(P)</th>
                                    <th>影响程度(I)</th>
                                    <th>风险指数(RI)</th>
                                    <th>概率等级</th>
                                    <th>影响等级</th>
                                    <th>风险等级</th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody">
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <p class="mt-2 mb-0">正在加载计算结果...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
const projectId = {{ project.id }};
let resultsData = null;
let weightConfig = null;

// 图表实例
let riskLevelChart = null;
let weightChart = null;
let riskIndexChart = null;

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadProjectResults();
    loadRiskMatrix();
});

// 加载项目结果
function loadProjectResults() {
    fetch(`/risk-assessment/api/project/${projectId}/results`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultsData = data;
                updateStatistics(data);
                renderCharts(data);
                renderResultsTable(data.results);
            } else {
                showError('加载结果失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error loading results:', error);
            showError('加载结果时发生错误');
        });
}

// 更新统计信息
function updateStatistics(data) {
    document.getElementById('totalCount').textContent = data.total_count;
    
    // 计算高风险数量
    const highRiskCount = (data.risk_level_distribution['高风险'] || 0) +
                         (data.risk_level_distribution['极高风险'] || 0);
    document.getElementById('highRiskCount').textContent = highRiskCount;
    
    // 平均风险指数
    const avgRiskIndex = data.average_scores.risk_index || 0;
    document.getElementById('avgRiskIndex').textContent = avgRiskIndex.toFixed(3);
    
    // 完成时间（这里简化显示）
    document.getElementById('completionTime').textContent = '刚刚';
}

// 渲染图表
function renderCharts(data) {
    renderRiskLevelChart(data.risk_level_distribution);
    renderWeightChart();
    renderRiskIndexChart(data.results);
}

// 渲染风险等级分布饼图
function renderRiskLevelChart(distribution) {
    const ctx = document.getElementById('riskLevelChart').getContext('2d');
    
    const labels = Object.keys(distribution);
    const values = Object.values(distribution);
    const colors = labels.map(level => {
        switch(level) {
            case '极低风险': return '#d4edda';
            case '低风险': return '#28a745';
            case '中风险': return '#ffc107';
            case '高风险': return '#fd7e14';
            case '极高风险': return '#dc3545';
            default: return '#6c757d';
        }
    });
    
    if (riskLevelChart) {
        riskLevelChart.destroy();
    }
    
    riskLevelChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: values,
                backgroundColor: colors,
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${context.label}: ${context.parsed} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// 渲染指标权重饼图
function renderWeightChart() {
    const ctx = document.getElementById('weightChart').getContext('2d');
    
    // 新版权重数据（基于5个指标）
    const weights = {
        '全年降水量': 0.633,
        '排水管网密度': 0.260,
        '平均高程值': 0.107,
        '人口密度': 0.240,  // 40% of 影响程度
        'GDP密度': 0.360    // 60% of 影响程度
    };
    
    const labels = Object.keys(weights);
    const values = Object.values(weights);
    const colors = ['#007bff', '#28a745', '#ffc107', '#dc3545'];
    
    if (weightChart) {
        weightChart.destroy();
    }
    
    weightChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: values,
                backgroundColor: colors,
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const percentage = (context.parsed * 100).toFixed(1);
                            return `${context.label}: ${percentage}%`;
                        }
                    }
                }
            }
        }
    });
}

// 渲染风险指数分布柱状图
function renderRiskIndexChart(results) {
    const ctx = document.getElementById('riskIndexChart').getContext('2d');
    
    // 按风险指数分组
    const bins = [0, 0.2, 0.4, 0.6, 0.8, 1.0];
    const binLabels = ['0-0.2', '0.2-0.4', '0.4-0.6', '0.6-0.8', '0.8-1.0'];
    const binCounts = new Array(bins.length - 1).fill(0);
    
    results.forEach(result => {
        const ri = result.risk_index || 0;
        for (let i = 0; i < bins.length - 1; i++) {
            if (ri >= bins[i] && ri < bins[i + 1]) {
                binCounts[i]++;
                break;
            }
        }
    });
    
    if (riskIndexChart) {
        riskIndexChart.destroy();
    }
    
    riskIndexChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: binLabels,
            datasets: [{
                label: '数据点数量',
                data: binCounts,
                backgroundColor: 'rgba(54, 162, 235, 0.6)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            return `风险指数范围: ${context[0].label}`;
                        }
                    }
                }
            }
        }
    });
}

// 渲染结果表格
function renderResultsTable(results) {
    const tbody = document.getElementById('resultsTableBody');
    
    if (!results || results.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" class="text-center py-4">暂无计算结果</td></tr>';
        return;
    }
    
    let html = '';
    results.forEach((result, index) => {
        const badgeClass = result.risk_level_badge_class || 'secondary';
        html += `
            <tr>
                <td><strong>${result.data_name || `数据${index + 1}`}</strong></td>
                <td>${result.location_name || '-'}</td>
                <td>${(result.likelihood_probability || 0).toFixed(3)}</td>
                <td>${(result.impact_degree || 0).toFixed(3)}</td>
                <td><strong>${(result.risk_index || 0).toFixed(3)}</strong></td>
                <td>${result.likelihood_level || '-'}</td>
                <td>${result.impact_level || '-'}</td>
                <td><span class="badge bg-${badgeClass}">${result.final_risk_level || '-'}</span></td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
}

// 加载风险矩阵
function loadRiskMatrix() {
    fetch('/risk-assessment/api/risk-matrix')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderRiskMatrix(data);
            } else {
                document.getElementById('riskMatrixContainer').innerHTML = 
                    '<div class="alert alert-danger">加载风险矩阵失败: ' + data.error + '</div>';
            }
        })
        .catch(error => {
            console.error('Error loading risk matrix:', error);
            document.getElementById('riskMatrixContainer').innerHTML = 
                '<div class="alert alert-danger">加载风险矩阵时发生错误</div>';
        });
}

// 渲染风险矩阵
function renderRiskMatrix(data) {
    let html = '<table class="table table-bordered risk-matrix-table">';
    html += '<thead><tr>';
    html += '<th class="text-center bg-light">发生概率 \\ 影响程度</th>';
    
    // 影响程度表头
    data.impact_headers.forEach(header => {
        html += `<th class="text-center bg-light">${header.level}</th>`;
    });
    html += '</tr></thead><tbody>';
    
    // 矩阵内容
    data.matrix_data.forEach(row => {
        html += '<tr>';
        html += `<td class="text-center bg-light"><strong>${row.likelihood_name}（${row.likelihood_code}）</strong></td>`;
        
        row.cells.forEach(cell => {
            const style = cell.style;
            html += `<td class="text-center" style="background-color: ${style.background}; color: ${style.color}; border-color: ${style.border};">
                        <strong>${cell.risk_level}</strong>
                     </td>`;
        });
        html += '</tr>';
    });
    
    html += '</tbody></table>';
    document.getElementById('riskMatrixContainer').innerHTML = html;
}

// 导出结果
function exportResults() {
    if (!resultsData) {
        alert('暂无数据可导出');
        return;
    }
    
    // 这里可以实现导出功能
    alert('导出功能即将推出');
}

// 显示错误信息
function showError(message) {
    const alertHtml = `<div class="alert alert-danger alert-dismissible fade show" role="alert">
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>`;
    
    document.querySelector('.container-fluid').insertAdjacentHTML('afterbegin', alertHtml);
}
</script>

<style>
.risk-matrix-table {
    font-size: 0.9rem;
}

.risk-matrix-table th,
.risk-matrix-table td {
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

.table td {
    font-size: 0.875rem;
    vertical-align: middle;
}

canvas {
    max-height: 300px;
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}
