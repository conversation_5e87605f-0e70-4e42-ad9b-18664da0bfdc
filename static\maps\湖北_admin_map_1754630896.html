<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
                <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/echarts.min.js"></script>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/v5/maps/hubei.js"></script>

    
</head>
<body >
    <div id="3820bf96e29d46f298faecb02b5dae0e" class="chart-container" style="width:900px; height:500px; "></div>
    <script>
        var chart_3820bf96e29d46f298faecb02b5dae0e = echarts.init(
            document.getElementById('3820bf96e29d46f298faecb02b5dae0e'), 'white', {renderer: 'canvas'});
        var option_3820bf96e29d46f298faecb02b5dae0e = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "aria": {
        "enabled": false
    },
    "color": [
        "#5470c6",
        "#91cc75",
        "#fac858",
        "#ee6666",
        "#73c0de",
        "#3ba272",
        "#fc8452",
        "#9a60b4",
        "#ea7ccc"
    ],
    "series": [
        {
            "type": "map",
            "name": "\u884c\u653f\u533a\u5212",
            "label": {
                "show": true,
                "color": "#2c3e50",
                "margin": 8,
                "fontSize": 10,
                "fontWeight": "bold",
                "valueAnimation": false
            },
            "map": "\u6e56\u5317",
            "data": [
                {
                    "name": "\u6b66\u6c49\u5e02",
                    "value": 1
                },
                {
                    "name": "\u9ec4\u77f3\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5341\u5830\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5b9c\u660c\u5e02",
                    "value": 1
                },
                {
                    "name": "\u8944\u9633\u5e02",
                    "value": 1
                },
                {
                    "name": "\u9102\u5dde\u5e02",
                    "value": 1
                },
                {
                    "name": "\u8346\u95e8\u5e02",
                    "value": 1
                },
                {
                    "name": "\u5b5d\u611f\u5e02",
                    "value": 1
                },
                {
                    "name": "\u8346\u5dde\u5e02",
                    "value": 1
                },
                {
                    "name": "\u9ec4\u5188\u5e02",
                    "value": 1
                },
                {
                    "name": "\u54b8\u5b81\u5e02",
                    "value": 1
                },
                {
                    "name": "\u968f\u5dde\u5e02",
                    "value": 1
                },
                {
                    "name": "\u6069\u65bd\u571f\u5bb6\u65cf\u82d7\u65cf\u81ea\u6cbb\u5dde",
                    "value": 1
                }
            ],
            "roam": true,
            "aspectScale": 0.75,
            "nameProperty": "name",
            "selectedMode": false,
            "zoom": 1.2,
            "zlevel": 0,
            "z": 2,
            "seriesLayoutBy": "column",
            "datasetIndex": 0,
            "mapValueCalculation": "sum",
            "showLegendSymbol": true,
            "itemStyle": {
                "color": "#a8e6cf",
                "borderColor": "#2c3e50",
                "borderWidth": 2
            },
            "emphasis": {},
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u884c\u653f\u533a\u5212"
            ],
            "selected": {},
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14,
            "backgroundColor": "transparent",
            "borderColor": "#ccc",
            "borderRadius": 0,
            "pageButtonItemGap": 5,
            "pageButtonPosition": "end",
            "pageFormatter": "{current}/{total}",
            "pageIconColor": "#2f4554",
            "pageIconInactiveColor": "#aaa",
            "pageIconSize": 15,
            "animationDurationUpdate": 800,
            "selector": false,
            "selectorPosition": "auto",
            "selectorItemGap": 7,
            "selectorButtonGap": 10
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "showContent": true,
        "alwaysShowContent": false,
        "showDelay": 0,
        "hideDelay": 100,
        "enterable": false,
        "confine": false,
        "appendToBody": false,
        "transitionDuration": 0.4,
        "formatter": "{b}<br/>\u884c\u653f\u7ea7\u522b: \u5730\u7ea7\u5e02",
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0,
        "padding": 5,
        "order": "seriesAsc"
    },
    "title": [
        {
            "show": true,
            "text": "\u6e56\u5317\u7701\u884c\u653f\u533a\u5212\u56fe",
            "target": "blank",
            "subtext": "\u5e02\u7ea7\u884c\u653f\u533a\u5212\u8fb9\u754c",
            "subtarget": "blank",
            "left": "center",
            "padding": 5,
            "itemGap": 10,
            "textAlign": "auto",
            "textVerticalAlign": "auto",
            "triggerEvent": false,
            "textStyle": {
                "color": "#2c3e50",
                "fontWeight": "bold",
                "fontSize": 18
            },
            "subtextStyle": {
                "color": "#7f8c8d",
                "fontSize": 12
            }
        }
    ],
    "visualMap": {
        "show": false,
        "type": "continuous",
        "min": 0,
        "max": 1,
        "inRange": {
            "color": [
                "#a8e6cf",
                "#a8e6cf"
            ]
        },
        "calculable": true,
        "inverse": false,
        "splitNumber": 5,
        "hoverLink": true,
        "orient": "vertical",
        "padding": 5,
        "showLabel": true,
        "itemWidth": 20,
        "itemHeight": 140,
        "borderWidth": 0
    }
};
        chart_3820bf96e29d46f298faecb02b5dae0e.setOption(option_3820bf96e29d46f298faecb02b5dae0e);
    </script>
</body>
</html>
