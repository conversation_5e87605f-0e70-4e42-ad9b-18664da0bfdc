{% extends "base.html" %}

{% block title %}地图展示 - 可视化分析 - {{ super() }}{% endblock %}

{% block extra_css %}
<style>
/* 右侧图层切换按钮样式 */
.layer-switch-btn {
    width: 100px;
    height: 60px;
    border-radius: 8px;
    border: 2px solid;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
}

.layer-switch-btn i {
    font-size: 18px;
    margin-bottom: 4px;
}

.layer-switch-btn .btn-text {
    font-size: 10px;
    line-height: 1;
    white-space: nowrap;
}

.layer-switch-btn:hover {
    transform: translateX(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.layer-switch-btn.active {
    background: rgba(13, 110, 253, 0.95);
    color: white;
    border-color: #0d6efd;
    transform: translateX(-5px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.4);
}

.layer-switch-btn.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #fff;
    border-radius: 0 2px 2px 0;
}

/* 不同图层按钮的颜色主题 */
.layer-switch-btn[data-layer="interactive"] {
    border-color: #0d6efd;
    color: #0d6efd;
}

.layer-switch-btn[data-layer="risk"] {
    border-color: #dc3545;
    color: #dc3545;
}

.layer-switch-btn[data-layer="risk"]:hover,
.layer-switch-btn[data-layer="risk"].active {
    background: rgba(220, 53, 69, 0.95);
    color: white;
    border-color: #dc3545;
}

.layer-switch-btn[data-layer="landuse"] {
    border-color: #198754;
    color: #198754;
}

.layer-switch-btn[data-layer="landuse"]:hover,
.layer-switch-btn[data-layer="landuse"].active {
    background: rgba(25, 135, 84, 0.95);
    color: white;
    border-color: #198754;
}

.layer-switch-btn[data-layer="admin"] {
    border-color: #0dcaf0;
    color: #0dcaf0;
}

.layer-switch-btn[data-layer="admin"]:hover,
.layer-switch-btn[data-layer="admin"].active {
    background: rgba(13, 202, 240, 0.95);
    color: white;
    border-color: #0dcaf0;
}

/* 图例样式 */
.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    margin-right: 8px;
    border: 1px solid #ddd;
}

/* 指北针样式 */
.compass-control {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #333;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    font-weight: bold;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
}

.compass-control:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

.compass-needle {
    position: relative;
    width: 30px;
    height: 30px;
}

.compass-needle::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 20px solid #dc3545;
}

.compass-needle::after {
    content: 'N';
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    font-weight: bold;
    color: #dc3545;
}

/* 比例尺样式 */
.scale-control {
    position: absolute;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid #333;
    border-radius: 4px;
    padding: 8px 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    font-size: 12px;
    color: #333;
    min-width: 120px;
}

.scale-line {
    height: 3px;
    background: #333;
    margin: 4px 0;
    position: relative;
}

.scale-line::before,
.scale-line::after {
    content: '';
    position: absolute;
    top: -2px;
    width: 1px;
    height: 7px;
    background: #333;
}

.scale-line::before {
    left: 0;
}

.scale-line::after {
    right: 0;
}

.scale-text {
    text-align: center;
    font-weight: bold;
    margin: 2px 0;
}

/* PyEcharts地图控件样式 */
.pyecharts-controls {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1000;
}

.pyecharts-controls .compass-control,
.pyecharts-controls .scale-control {
    pointer-events: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #layerSwitchButtons {
        right: 10px;
        top: 10px;
        transform: none;
    }

    .layer-switch-btn {
        width: 80px;
        height: 50px;
    }

    .layer-switch-btn i {
        font-size: 16px;
    }

    .layer-switch-btn .btn-text {
        font-size: 9px;
    }

    .compass-control {
        width: 50px;
        height: 50px;
        top: 15px;
        left: 15px;
    }

    .compass-needle {
        width: 25px;
        height: 25px;
    }

    .compass-needle::before {
        border-left-width: 5px;
        border-right-width: 5px;
        border-bottom-width: 16px;
    }

    .compass-needle::after {
        font-size: 10px;
    }

    .scale-control {
        bottom: 15px;
        left: 15px;
        padding: 6px 10px;
        font-size: 11px;
        min-width: 100px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-map-marked-alt me-2"></i>地图展示
                    </h2>
                    <p class="text-muted mb-0">淹没高程、淹没范围可视化展示</p>
                </div>
                <div>
                    <span class="badge bg-info me-2">模拟数据 - 基于真实DEM和历史内涝分析</span>
                    <a href="{{ url_for('visualization.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 控制面板 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <!-- 省份选择 -->
                        <div class="col-md-2">
                            <label for="provinceSelect" class="form-label mb-1">选择省份</label>
                            <select class="form-select form-select-sm" id="provinceSelect">
                                <option value="">请选择省份...</option>
                                <!-- 直辖市 -->
                                <option value="北京市">北京市</option>
                                <option value="天津市">天津市</option>
                                <option value="上海市">上海市</option>
                                <option value="重庆市">重庆市</option>
                                <!-- 华北地区 -->
                                <option value="河北省">河北省</option>
                                <option value="山西省">山西省</option>
                                <!-- 东北地区 -->
                                <option value="辽宁省">辽宁省</option>
                                <option value="吉林省">吉林省</option>
                                <option value="黑龙江省">黑龙江省</option>
                                <!-- 华东地区 -->
                                <option value="江苏省">江苏省</option>
                                <option value="浙江省">浙江省</option>
                                <option value="安徽省">安徽省</option>
                                <option value="福建省">福建省</option>
                                <option value="江西省">江西省</option>
                                <option value="山东省">山东省</option>
                                <option value="台湾省">台湾省</option>
                                <!-- 华中地区 -->
                                <option value="河南省">河南省</option>
                                <option value="湖北省">湖北省</option>
                                <option value="湖南省">湖南省</option>
                                <!-- 华南地区 -->
                                <option value="广东省">广东省</option>
                                <option value="海南省">海南省</option>
                                <!-- 西南地区 -->
                                <option value="四川省">四川省</option>
                                <option value="贵州省">贵州省</option>
                                <option value="云南省">云南省</option>
                                <!-- 西北地区 -->
                                <option value="陕西省">陕西省</option>
                                <option value="甘肃省">甘肃省</option>
                                <option value="青海省">青海省</option>
                                <!-- 自治区 -->
                                <option value="内蒙古自治区">内蒙古自治区</option>
                                <option value="广西壮族自治区">广西壮族自治区</option>
                                <option value="西藏自治区">西藏自治区</option>
                                <option value="宁夏回族自治区">宁夏回族自治区</option>
                                <option value="新疆维吾尔自治区">新疆维吾尔自治区</option>
                                <!-- 特别行政区 -->
                                <option value="香港特别行政区">香港特别行政区</option>
                                <option value="澳门特别行政区">澳门特别行政区</option>
                            </select>
                        </div>

                        <!-- 年份选择 -->
                        <div class="col-md-2" id="yearSelectContainer" style="display: none;">
                            <label for="yearSelect" class="form-label mb-1">选择年份</label>
                            <select class="form-select form-select-sm" id="yearSelect">
                                <option value="">请选择年份...</option>
                            </select>
                        </div>

                        <!-- 地图底图 -->
                        <div class="col-md-1">
                            <label for="baseMapSelect" class="form-label mb-1">底图</label>
                            <select class="form-select form-select-sm" id="baseMapSelect">
                                <option value="gaode">高德</option>
                                <option value="satellite">卫星</option>
                            </select>
                        </div>
                        
                        <!-- 工具栏 -->
                        <div class="col-md-3">
                            <label class="form-label mb-1">地图工具</label>
                            <div class="d-flex gap-1">
                                <button class="btn btn-sm btn-outline-primary" id="fullscreenBtn" title="全屏">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success" id="refreshBtn" title="刷新">
                                    <i class="fas fa-sync"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info" id="measureBtn" title="测量">
                                    <i class="fas fa-ruler"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 地图和侧边栏 -->
    <div class="row">
        <!-- 地图主区域 -->
        <div class="col-lg-9">
            <div class="card">
                <div class="card-body p-0">
                    <div id="mapContainer" style="height: 600px; position: relative;">
                        <!-- Leaflet地图容器 -->
                        <div id="leafletMap" style="height: 100%; width: 100%;"></div>

                        <!-- PyEcharts地图容器 -->
                        <div id="pyechartsMap" style="height: 100%; width: 100%; display: none;">
                            <iframe id="pyechartsFrame" style="width: 100%; height: 100%; border: none;"></iframe>
                        </div>

                        <!-- 指北针控件 -->
                        <div class="compass-control" id="compassControl" title="指北针">
                            <div class="compass-needle"></div>
                        </div>

                        <!-- 比例尺控件 -->
                        <div class="scale-control" id="scaleControl">
                            <div class="scale-text" id="scaleText">1:100,000</div>
                            <div class="scale-line" id="scaleLine" style="width: 100px;"></div>
                            <div class="scale-text">0 ——— 10km</div>
                        </div>

                        <!-- 右侧图层切换按钮 -->
                        <div id="layerSwitchButtons" style="position: absolute; top: 50%; right: 15px; transform: translateY(-50%); z-index: 1000;">
                            <div class="d-flex flex-column gap-2">
                                <!-- 交互地图按钮 -->
                                <button class="btn btn-primary layer-switch-btn active"
                                        data-layer="interactive"
                                        title="交互地图"
                                        onclick="switchToLayer('interactive')">
                                    <i class="fas fa-layer-group"></i>
                                    <span class="btn-text">交互地图</span>
                                </button>

                                <!-- 风险区划图按钮 -->
                                <button class="btn btn-outline-danger layer-switch-btn"
                                        data-layer="risk"
                                        title="风险区划图"
                                        onclick="switchToLayer('risk')">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span class="btn-text">风险区划</span>
                                </button>

                                <!-- 土地利用图按钮 -->
                                <button class="btn btn-outline-success layer-switch-btn"
                                        data-layer="landuse"
                                        title="土地利用图"
                                        onclick="switchToLayer('landuse')">
                                    <i class="fas fa-seedling"></i>
                                    <span class="btn-text">土地利用</span>
                                </button>

                                <!-- 行政区划图按钮 -->
                                <button class="btn btn-outline-info layer-switch-btn"
                                        data-layer="admin"
                                        title="行政区划图"
                                        onclick="switchToLayer('admin')">
                                    <i class="fas fa-map-marked-alt"></i>
                                    <span class="btn-text">行政区划</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 地图状态栏 -->
                <div class="card-footer bg-light">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <small class="text-muted">
                                <i class="fas fa-mouse-pointer me-1"></i>
                                坐标: <span id="mouseCoords">--</span>
                            </small>
                        </div>
                        <div class="col-md-4 text-center">
                            <small class="text-muted">
                                <i class="fas fa-search me-1"></i>
                                缩放: <span id="zoomLevel">--</span>
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <small class="text-muted">
                                <i class="fas fa-layer-group me-1"></i>
                                图层: <span id="activeLayersCount">0</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 侧边栏 -->
        <div class="col-lg-3">
            <!-- 图例 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>图例
                    </h6>
                </div>
                <div class="card-body">
                    <div id="legendContent">
                        <p class="text-muted text-center">请选择城市和图层</p>
                    </div>
                </div>
            </div>
            
            <!-- 统计信息 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>统计信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <div class="border-bottom pb-2">
                                <h5 class="text-primary mb-1" id="totalPoints">--</h5>
                                <small class="text-muted">数据点数量</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="text-danger mb-1" id="highRiskCount">--</h6>
                                <small class="text-muted">高风险</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="text-warning mb-1" id="mediumRiskCount">--</h6>
                            <small class="text-muted">中风险</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 风险等级图例 -->
            <div class="card mb-3" id="riskLegendCard" style="display: none;">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-palette me-2"></i>风险等级图例
                    </h6>
                </div>
                <div class="card-body py-2">
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #d4edda;"></div>
                        <small>极低风险</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #28a745;"></div>
                        <small>低风险</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #ffc107;"></div>
                        <small>中风险</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #fd7e14;"></div>
                        <small>高风险</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #dc3545;"></div>
                        <small>极高风险</small>
                    </div>
                </div>
            </div>

            <!-- 土地利用图例 -->
            <div class="card mb-3" id="landuseLegendCard" style="display: none;">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-leaf me-2"></i>土地利用图例
                    </h6>
                </div>
                <div class="card-body py-2">
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #FFD700;"></div>
                        <small>耕地</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #228B22;"></div>
                        <small>林地</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #90EE90;"></div>
                        <small>草地</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #4169E1;"></div>
                        <small>水域</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #808080;"></div>
                        <small>建设用地</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #D2B48C;"></div>
                        <small>未利用地</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #FFB6C1;"></div>
                        <small>住宅用地</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #FF6347;"></div>
                        <small>商业用地</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #8B4513;"></div>
                        <small>工业用地</small>
                    </div>
                    <div class="legend-item mb-2">
                        <div class="legend-color" style="background-color: #696969;"></div>
                        <small>交通用地</small>
                    </div>
                </div>
            </div>

            <!-- 图层控制面板已移除 -->
            <div class="card mb-3" id="layerControlPanel" style="display: none !important; visibility: hidden;">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-layer-group me-2"></i>图层控制
                    </h6>
                </div>
                <div class="card-body py-2">
                    <!-- 风险区划图层 -->
                    <div class="layer-item mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="riskBoundariesToggle"
                                       onchange="toggleLayer('riskBoundaries')">
                                <label class="form-check-label" for="riskBoundariesToggle">
                                    <i class="fas fa-exclamation-triangle text-danger me-1"></i>风险区划
                                </label>
                            </div>
                        </div>
                        <div class="opacity-control mt-2" style="display: none;" id="riskBoundariesOpacityControl">
                            <small class="text-muted">透明度:</small>
                            <input type="range" class="form-range opacity-slider" min="0" max="100" value="70"
                                   id="riskBoundariesOpacity" onchange="updateLayerOpacity('riskBoundaries', this.value)">
                            <small class="opacity-value" id="riskBoundariesOpacityValue">70%</small>
                        </div>
                    </div>

                    <!-- 土地利用图层 -->
                    <div class="layer-item mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="landuseBoundariesToggle"
                                       onchange="toggleLayer('landuseBoundaries')">
                                <label class="form-check-label" for="landuseBoundariesToggle">
                                    <i class="fas fa-leaf text-success me-1"></i>土地利用
                                </label>
                            </div>
                        </div>
                        <div class="opacity-control mt-2" style="display: none;" id="landuseBoundariesOpacityControl">
                            <small class="text-muted">透明度:</small>
                            <input type="range" class="form-range opacity-slider" min="0" max="100" value="60"
                                   id="landuseBoundariesOpacity" onchange="updateLayerOpacity('landuseBoundaries', this.value)">
                            <small class="opacity-value" id="landuseBoundariesOpacityValue">60%</small>
                        </div>
                    </div>

                    <!-- 行政区划图层 -->
                    <div class="layer-item mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="adminToggle"
                                       onchange="toggleLayer('admin')">
                                <label class="form-check-label" for="adminToggle">
                                    <i class="fas fa-map-marked-alt text-info me-1"></i>行政区划
                                </label>
                            </div>
                        </div>
                        <div class="opacity-control mt-2" style="display: none;" id="adminOpacityControl">
                            <small class="text-muted">透明度:</small>
                            <input type="range" class="form-range opacity-slider" min="0" max="100" value="50"
                                   id="adminOpacity" onchange="updateLayerOpacity('admin', this.value)">
                            <small class="opacity-value" id="adminOpacityValue">50%</small>
                        </div>
                    </div>

                    <!-- 热力图层 -->
                    <div class="layer-item mb-2">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="heatmapToggle"
                                       onchange="toggleLayer('heatmap')">
                                <label class="form-check-label" for="heatmapToggle">
                                    <i class="fas fa-fire text-warning me-1"></i>热力图
                                </label>
                            </div>
                        </div>
                        <div class="opacity-control mt-2" style="display: none;" id="heatmapOpacityControl">
                            <small class="text-muted">透明度:</small>
                            <input type="range" class="form-range opacity-slider" min="0" max="100" value="80"
                                   id="heatmapOpacity" onchange="updateLayerOpacity('heatmap', this.value)">
                            <small class="opacity-value" id="heatmapOpacityValue">80%</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<link rel="stylesheet" href="{{ url_for('static', filename='css/visualization.css') }}" />
<link rel="stylesheet" href="{{ url_for('static', filename='css/map-controls.css') }}" />
<style>
#mapContainer {
    border-radius: 0.375rem;
    overflow: hidden;
    background-color: #f8f9fa;
    position: relative;
}

.leaflet-container {
    height: 100%;
    width: 100%;
}

.btn-group .btn-check:checked + .btn {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

.form-range::-webkit-slider-thumb {
    background-color: var(--bs-primary);
}

.form-range::-moz-range-thumb {
    background-color: var(--bs-primary);
    border: none;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    margin-right: 8px;
    border: 1px solid #ddd;
}

.fullscreen-map {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: white;
}

.fullscreen-map .card-body {
    padding: 0 !important;
}

.fullscreen-map #mapContainer {
    height: 100vh !important;
    border-radius: 0 !important;
}

/* 全屏退出按钮样式 */
.fullscreen-exit-btn {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 10000 !important;
    background: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid #ddd !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
}

.fullscreen-exit-btn:hover {
    background: white !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2) !important;
}

.map-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.feature-highlight {
    border: 2px solid #007bff !important;
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.5) !important;
}

/* GIS图层面板样式 */
.layer-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.layer-panel-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.layer-panel-actions {
    display: flex;
    gap: 4px;
}

.layer-panel-body {
    padding: 16px;
}

.layer-section {
    margin-bottom: 1rem;
}

.layer-section:last-child {
    margin-bottom: 0;
}

.layer-section-header h6 {
    color: #495057;
    font-weight: 600;
}

.layer-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.layer-item:hover {
    background: #e9ecef;
    border-color: #dee2e6;
}

.layer-item:last-child {
    margin-bottom: 0;
}

.layer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.layer-name {
    font-weight: 500;
    color: #495057;
    flex: 1;
}

.layer-controls {
    display: flex;
    align-items: center;
    gap: 4px;
}

.opacity-control {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
}

.opacity-label {
    color: #6c757d;
    min-width: 50px;
}

.opacity-slider {
    flex: 1;
    min-width: 80px;
}

.opacity-value {
    color: #495057;
    font-weight: 500;
    min-width: 35px;
    text-align: right;
}

.gis-layer-item {
    border-left: 4px solid #007bff;
}

.gis-layer-item .layer-name {
    color: #007bff;
}

/* 文件上传预览样式 */
#selectedFilesPreview .list-group-item {
    padding: 8px 12px;
    font-size: 0.875rem;
}

/* 图层集成工具样式 */
.integration-tool {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 10px;
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tool-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
}

.integration-tool .btn {
    padding: 4px 8px;
    font-size: 0.75rem;
}

/* 对齐检查结果样式 */
#alignmentResult .card,
#groupResult .card,
#integrationResult .card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#alignmentResult .card-header,
#groupResult .card-header,
#integrationResult .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

/* 图层选择列表样式 */
#layerSelectionList {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 10px;
    background: #f8f9fa;
}

#layerSelectionList .form-check {
    margin-bottom: 8px;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

#layerSelectionList .form-check:hover {
    background-color: #e9ecef;
}

/* 模态框样式增强 */
.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
<script src="{{ url_for('static', filename='js/map-controls.js') }}"></script>
<script src="{{ url_for('static', filename='js/visualization.js') }}"></script>
<script>
// 注意：全局变量已在 visualization.js 中声明，这里不需要重复声明

// 省份配置已在 visualization.js 中定义，这里不需要重复声明



// 图层切换功能 - 必须在全局作用域中定义，以便HTML onclick属性可以访问
function switchToLayer(layerType) {
    console.log(`切换到图层: ${layerType}`);

    try {
        // 更新按钮状态
        document.querySelectorAll('.layer-switch-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        const targetBtn = document.querySelector(`[data-layer="${layerType}"]`);
        if (targetBtn) {
            targetBtn.classList.add('active');
        } else {
            console.error(`找不到图层按钮: ${layerType}`);
        }

        // 确保地图容器存在
        const leafletMap = document.getElementById('leafletMap');
        const pyechartsMap = document.getElementById('pyechartsMap');

        if (!leafletMap || !pyechartsMap) {
            console.error('地图容器不存在');
            return;
        }

        // 通用清理函数 - 清除所有图层内容
        function clearAllMapContent() {
            // 清除iframe内容
            $('#pyechartsFrame').attr('src', '');
            // 清除容器innerHTML（土地利用图层使用）
            const pyechartsMapContainer = document.getElementById('pyechartsMap');
            if (pyechartsMapContainer) {
                pyechartsMapContainer.innerHTML = '<iframe id="pyechartsFrame" style="width: 100%; height: 100%; border: none;"></iframe>';
            }
            // 隐藏所有图例
            $('#riskLegendCard').hide();
            $('#landuseLegendCard').hide();
        }

        // 根据图层类型执行不同的操作
        switch(layerType) {
            case 'interactive':
                console.log('切换到交互地图模式');
                // 切换到交互地图
                leafletMap.style.display = 'block';
                pyechartsMap.style.display = 'none';

                // 隐藏年份选择器
                $('#yearSelectContainer').hide();

                // 清除所有图层内容
                clearAllMapContent();

                // 清除所有数据图层，只保留底图
                clearAllLayers();

                // 显示所有图层控制（已禁用）
                // showLayerControls();
                
                // 更新地图控件
                if (mapControls) {
                    mapControls.updateScale('interactive');
                }
                break;

            case 'risk':
                console.log('🔄 切换到风险区划图模式');
                // 切换到风险区划图
                leafletMap.style.display = 'none';
                pyechartsMap.style.display = 'block';

                // 清除所有图层内容
                clearAllMapContent();

                // 清除土地利用统计数据
                window.currentLanduseStatistics = null;

                // 显示年份选择器
                $('#yearSelectContainer').show();

                // 检查是否已经选择了省份
                const currentSelectedProvince = $('#provinceSelect').val();
                console.log(`🏛️ 当前选择的省份: ${currentSelectedProvince}`);

                if (currentSelectedProvince) {
                    // 如果已经选择了省份，立即加载年份数据
                    console.log(`🔄 已选择省份 ${currentSelectedProvince}，开始加载年份数据...`);
                    $('#yearSelect').html('<option value="">正在加载年份数据...</option>');
                    loadProvinceYearsForRisk(currentSelectedProvince);
                    showAlert(`正在加载 ${currentSelectedProvince} 的年份数据...`, 'info');
                } else {
                    // 如果没有选择省份，显示提示
                    $('#yearSelect').html('<option value="">请选择年份...</option>');
                    showAlert('请先选择省份，然后选择年份来查看风险区划图', 'info');
                }

                showPyEchartsPlaceholder();

                // 隐藏风险等级图例，等待数据加载后再显示
                $('#riskLegendCard').hide();
                
                // 更新地图控件
                if (mapControls) {
                    mapControls.updateScale('risk', currentSelectedProvince);
                }
                break;

            case 'landuse':
                console.log('🌱 切换到土地利用图模式');
                // 切换到土地利用图（使用PyEcharts）
                leafletMap.style.display = 'none';
                pyechartsMap.style.display = 'block';

                // 隐藏年份选择器
                $('#yearSelectContainer').hide();

                // 清除所有图层内容
                clearAllMapContent();

                // 检查是否选择了省份
                const selectedProvince = $('#provinceSelect').val();
                if (selectedProvince) {
                    console.log(`🗺️ 开始生成 ${selectedProvince} 的土地利用地图`);

                    // 显示加载状态
                    showMapLoading('正在加载土地利用数据...');

                    // 先检查是否有土地利用数据
                    checkLanduseDataAvailability(selectedProvince)
                        .then(hasData => {
                            if (hasData) {
                                generatePyEchartsLanduseMap(selectedProvince);
                                $('#landuseLegendCard').show();
                            } else {
                                showPyEchartsPlaceholder(
                                    `暂无 ${selectedProvince} 的土地利用数据。请通过数据导入页面上传TIFF格式的土地利用数据文件。`,
                                    'landuse'
                                );
                                showAlert(`暂无 ${selectedProvince} 的土地利用数据，请先上传数据`, 'warning');
                            }
                        })
                        .catch(error => {
                            console.error('检查土地利用数据失败:', error);
                            showPyEchartsPlaceholder('数据检查失败，请稍后重试', 'landuse');
                            showAlert('数据检查失败，请稍后重试', 'error');
                        })
                        .finally(() => {
                            hideMapLoading();
                        });
                } else {
                    console.log('⚠️ 未选择省份');
                    showAlert('请先选择省份，然后查看土地利用图', 'info');
                    showPyEchartsPlaceholder('请选择省份查看土地利用图', 'landuse');
                }

                // 隐藏其他图例
                $('#riskLegendCard').hide();
                
                // 更新地图控件
                if (mapControls) {
                    mapControls.updateScale('landuse', selectedProvince);
                }
                break;

            case 'admin':
                console.log('切换到行政区划图模式');
                // 切换到行政区划图（使用PyEcharts）
                leafletMap.style.display = 'none';
                pyechartsMap.style.display = 'block';

                // 隐藏年份选择器
                $('#yearSelectContainer').hide();

                // 清除所有图层内容
                clearAllMapContent();

                // 清除土地利用统计数据
                window.currentLanduseStatistics = null;

                // 检查是否选择了省份
                const selectedProvinceAdmin = $('#provinceSelect').val();
                if (selectedProvinceAdmin) {
                    // 生成行政区划地图
                    generateAdminPyEchartsMap(selectedProvinceAdmin);
                } else {
                    console.log('⚠️ 未选择省份');
                    showAlert('请先选择省份，然后查看行政区划图', 'info');
                    showPyEchartsPlaceholder('请选择省份查看行政区划图', 'admin');
                }

                // 隐藏其他图例
                $('#riskLegendCard').hide();
                $('#landuseLegendCard').hide();
                
                // 更新地图控件
                if (mapControls) {
                    mapControls.updateScale('admin', selectedProvinceAdmin);
                }
                break;

            default:
                console.warn(`未知的图层类型: ${layerType}`);
                return;
        }

        // 更新图例
        updateLegend(layerType);

        console.log(`图层切换完成: ${layerType}`);

    } catch (error) {
        console.error('图层切换失败:', error);
    }
}

// 确保switchToLayer函数在全局作用域中可用
window.switchToLayer = switchToLayer;

// 绑定地图工具按钮事件
function bindMapToolEvents() {
    console.log('绑定地图工具按钮事件...');

    // 全屏按钮事件
    $('#fullscreenBtn').off('click').on('click', function() {
        console.log('全屏按钮被点击');
        const mapCard = $('#mapContainer').closest('.card');
        if (mapCard.hasClass('fullscreen-map')) {
            exitFullscreen();
        } else {
            enterFullscreen();
        }
    });

    // 刷新按钮事件
    $('#refreshBtn').off('click').on('click', function() {
        console.log('刷新按钮被点击');
        refreshMap();
    });

    // 测量按钮事件
    $('#measureBtn').off('click').on('click', function() {
        console.log('测量按钮被点击');
        if (measureTool.active) {
            stopMeasuring();
        } else {
            startMeasuring();
        }
    });

    console.log('地图工具按钮事件绑定完成');
}

// 页面初始化代码
$(document).ready(function() {
    // 初始化地图和事件绑定
    initializeMap();
    bindEvents();

    // 绑定地图工具按钮事件
    bindMapToolEvents();

    // 从URL参数获取省份
    const urlParams = new URLSearchParams(window.location.search);
    const provinceParam = urlParams.get('province');
    if (provinceParam) {
        $('#provinceSelect').val(provinceParam);
        // 注释掉自动加载省份数据，避免在交互地图上显示假数据
        // loadProvinceData(provinceParam);
    }

    // 初始化地图状态栏
    setTimeout(function() {
        if (map) {
            $('#zoomLevel').text(map.getZoom());
        }
    }, 500);

    // 绑定地图类型切换事件
    $('input[name="mapType"]').on('change', function() {
        const mapType = $(this).val();
        switchMapType(mapType);
    });

    // 绑定省份选择事件 - 使用事件委托确保事件始终有效
    $(document).on('change', '#provinceSelect', function() {
        const selectedProvince = $(this).val();
        currentProvince = selectedProvince;

        console.log(`🏛️ 省份选择器变化: 选中省份=${selectedProvince}`);

        // 检查当前是否在风险区划模式
        const activeLayer = document.querySelector('.layer-switch-btn.active');
        const isRiskMode = activeLayer && activeLayer.getAttribute('data-layer') === 'risk';

        console.log(`🎯 当前模式: ${isRiskMode ? '风险区划' : '其他'}`);
        console.log(`🔍 活动图层按钮:`, activeLayer);

        if (isRiskMode) {
            if (selectedProvince) {
                console.log(`🔄 开始加载 ${selectedProvince} 的年份数据...`);
                // 先更新年份选择器显示加载状态
                const yearSelect = $('#yearSelect');
                console.log(`📅 年份选择器元素:`, yearSelect.length > 0 ? '找到' : '未找到');
                yearSelect.html('<option value="">正在加载年份数据...</option>');

                // 立即调用年份数据加载函数
                loadProvinceYearsForRisk(selectedProvince);
            } else {
                console.log('⚠️ 省份未选择，重置年份选择器');
                $('#yearSelect').html('<option value="">请选择年份...</option>');
            }
        } else {
            console.log(`ℹ️ 非风险区划模式，跳过年份数据加载`);
        }
    });

    // 绑定年份选择事件 - 使用事件委托确保事件始终有效
    console.log('正在绑定年份选择器事件...');
    $(document).on('change', '#yearSelect', function() {
        const selectedYear = $(this).val();
        const selectedProvince = $('#provinceSelect').val();
        currentYear = selectedYear;

        console.log(`📅 年份选择器变化: 选中年份=${selectedYear}, 当前省份=${selectedProvince}`);

        if (selectedProvince && selectedYear) {
            console.log(`🗺️ 开始生成新的地图: 省份=${selectedProvince}, 年份=${selectedYear}`);
            // 生成PyEcharts风险区划图
            generatePyEchartsMap(selectedProvince, selectedYear);
        } else {
            console.warn('⚠️ 省份或年份未完全选择，无法生成地图');
        }
    });

    // 测试年份选择器是否存在
    console.log('年份选择器元素数量:', $('#yearSelect').length);

    // 添加点击事件测试
    $('#yearSelect').on('click', function() {
        console.log('年份选择器被点击了');
    });

    // 设置默认图层的逻辑已移动到页面底部的DOMContentLoaded事件中
});


// 加载省份年份数据（用于风险区划模式）
function loadProvinceYearsForRisk(provinceName) {
    console.log(`🔄 开始加载 ${provinceName} 的年份数据...`);

    // 确保年份选择器容器可见
    $('#yearSelectContainer').show();

    const apiUrl = `/api/provinces/${encodeURIComponent(provinceName)}/years`;
    console.log(`🌐 发送API请求: ${apiUrl}`);

    fetch(apiUrl)
        .then(response => {
            console.log(`📡 收到 ${provinceName} 年份数据响应, 状态码: ${response.status}`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log(`📊 年份数据解析结果:`, data);

            const yearSelect = $('#yearSelect');

            if (data.success && data.years && data.years.length > 0) {
                console.log(`✅ 找到 ${data.years.length} 个年份`);

                // 更新年份选择器
                yearSelect.html('<option value="">请选择年份...</option>');

                data.years.forEach(yearInfo => {
                    yearSelect.append(`<option value="${yearInfo.year}">${yearInfo.year}年 (${yearInfo.project_count}个项目)</option>`);
                });

                // 不需要重新绑定事件，因为我们使用了事件委托
                console.log(`✅ 年份选择器选项已更新，事件委托会自动处理选择事件`);

                // 触发一个自定义事件来通知年份数据已加载
                yearSelect.trigger('yearsLoaded', [data.years]);

                // 显示成功消息
                showAlert(`✅ 已加载 ${provinceName} 的 ${data.years.length} 个年份数据，请选择年份查看风险区划图`, 'success');
                console.log(`✅ 成功加载 ${data.years.length} 个年份，等待用户选择`);

            } else if (data.success) {
                // API调用成功但没有年份数据
                console.warn(`⚠️ ${provinceName} 未找到年份数据`);
                yearSelect.html('<option value="">暂无年份数据</option>');

                // 显示友好提示而不是警告
                const message = data.message || `${provinceName} 暂无风险区划数据，请先导入相关数据`;
                showAlert(message, 'info');
                console.log(`ℹ️ ${message}`);
            } else {
                // API调用失败
                console.error(`❌ ${provinceName} 年份数据加载失败:`, data.error);
                yearSelect.html('<option value="">暂无年份数据</option>');
                showAlert(data.error || `加载 ${provinceName} 年份数据失败`, 'warning');
            }
        })
        .catch(error => {
            console.error(`❌ 加载 ${provinceName} 年份数据失败:`, error);
            $('#yearSelect').html('<option value="">暂无年份数据</option>');

            // 根据错误类型显示不同的提示
            let errorMessage;
            if (error.message.includes('404')) {
                errorMessage = `${provinceName} 暂无年份数据，请先导入相关数据`;
            } else if (error.message.includes('网络')) {
                errorMessage = '网络连接异常，请检查网络后重试';
            } else {
                errorMessage = `无法加载 ${provinceName} 的年份数据`;
            }

            showAlert(errorMessage, 'warning');
        });
}

function displayProvinceRiskMarkers(data) {
    // 清除现有风险图层
    if (mapLayers.risk) {
        map.removeLayer(mapLayers.risk);
    }

    // 风险等级颜色配置（参考用户示例图片的颜色）
    const riskColors = {
        '极低风险': '#d4edda',    // 浅绿色
        '低风险': '#28a745',      // 绿色
        '中风险': '#ffc107',      // 黄色
        '高风险': '#fd7e14',      // 橙色
        '极高风险': '#dc3545',    // 红色
        '极低': '#d4edda',
        '低': '#28a745',
        '中': '#ffc107',
        '高': '#fd7e14',
        '极高': '#dc3545',
        'very_low': '#d4edda',
        'low': '#28a745',
        'medium': '#ffc107',
        'high': '#fd7e14',
        'very_high': '#dc3545'
    };

    const riskMarkers = data.map(cityRisk => {
        const color = riskColors[cityRisk.risk_level] || '#6c757d';

        // 根据风险等级调整标记大小
        let radius = 10;
        if (cityRisk.risk_level === '极高风险' || cityRisk.risk_level === 'very_high') {
            radius = 15;
        } else if (cityRisk.risk_level === '高风险' || cityRisk.risk_level === 'high') {
            radius = 13;
        } else if (cityRisk.risk_level === '中风险' || cityRisk.risk_level === 'medium') {
            radius = 11;
        } else if (cityRisk.risk_level === '极低风险' || cityRisk.risk_level === 'very_low') {
            radius = 8;
        }

        // 兼容不同的坐标字段名
        const lat = cityRisk.latitude || cityRisk.lat;
        const lng = cityRisk.longitude || cityRisk.lng;

        if (!lat || !lng) {
            console.warn('跳过无坐标数据:', cityRisk);
            return null;
        }

        const marker = L.circleMarker([lat, lng], {
            radius: radius,
            fillColor: color,
            color: '#fff',
            weight: 3,
            opacity: 1,
            fillOpacity: 0.9
        });

        marker.bindPopup(`
            <div style="min-width: 200px;">
                <h6 style="margin-bottom: 10px; color: #333; border-bottom: 2px solid ${color}; padding-bottom: 5px;">
                    ${cityRisk.city_name || cityRisk.location} 内涝风险评估
                </h6>
                <div style="margin-bottom: 8px;">
                    <strong>风险等级:</strong>
                    <span style="color: ${color}; font-weight: bold; background: ${color}20; padding: 2px 6px; border-radius: 3px;">
                        ${cityRisk.risk_level}
                    </span>
                </div>
                <div style="margin-bottom: 5px;">
                    <strong>风险指数:</strong> ${cityRisk.risk_index ? cityRisk.risk_index.toFixed(3) : 'N/A'}
                </div>
                ${cityRisk.elevation ? `<div style="margin-bottom: 5px;"><strong>海拔高度:</strong> ${cityRisk.elevation.toFixed(1)}m</div>` : ''}
                ${cityRisk.rainfall ? `<div style="margin-bottom: 5px;"><strong>降雨量:</strong> ${cityRisk.rainfall.toFixed(1)}mm</div>` : ''}
                ${cityRisk.drainage_capacity ? `<div style="margin-bottom: 5px;"><strong>排水能力:</strong> ${cityRisk.drainage_capacity.toFixed(2)}</div>` : ''}
                ${cityRisk.assessment_year ? `<div style="margin-bottom: 5px;"><strong>评估年份:</strong> ${cityRisk.assessment_year}年</div>` : ''}
                <div style="margin-top: 8px; font-size: 12px; color: #666;">
                    <strong>坐标:</strong> ${lat.toFixed(4)}, ${lng.toFixed(4)}
                </div>
            </div>
        `);

        return marker;
    }).filter(marker => marker !== null); // 过滤掉无效的标记

    mapLayers.risk = L.layerGroup(riskMarkers).addTo(map);
    updateActiveLayersCount();
}

function displayAdminLayer(data) {
    if (mapLayers.admin) {
        map.removeLayer(mapLayers.admin);
    }

    // 创建图层组
    mapLayers.admin = L.layerGroup();

    // 添加每个行政区域
    data.forEach(district => {
        const polygon = L.polygon(district.coordinates, {
            color: '#28a745',
            fillColor: '#28a745',
            fillOpacity: 0.1,
            weight: 2
        }).bindPopup(`
            <div>
                <h6>${district.name}</h6>
                <p><strong>人口:</strong> ${district.population.toLocaleString()}人</p>
                <p><strong>面积:</strong> ${district.area_km2} km²</p>
                <p><strong>内涝风险:</strong> ${district.flood_risk_level}</p>
                <p><strong>数据来源:</strong> ${district.source === 'uploaded_data' ? '上传数据' : '系统数据'}</p>
            </div>
        `);

        mapLayers.admin.addLayer(polygon);
    });

    mapLayers.admin.addTo(map);

    updateActiveLayersCount();
}


function getRiskLevelText(level) {
    const levels = {
        'very_high': '极高风险',
        'high': '高风险',
        'medium': '中风险',
        'low': '低风险',
        'very_low': '极低风险'
    };
    return levels[level] || level;
}

function toggleRiskLayer(show) {
    if (show && currentCity) {
        loadRiskLayer(currentCity);
    } else if (mapLayers.risk) {
        map.removeLayer(mapLayers.risk);
        mapLayers.risk = null;
        updateActiveLayersCount();
    }
}

function toggleAdminLayer(show) {
    if (show) {
        // 优先使用省份，其次使用城市
        const selectedProvince = $('#provinceSelect').val();
        const selectedCity = currentCity;

        if (selectedProvince && selectedProvince !== '全国') {
            loadAdminLayer(null, selectedProvince);
        } else if (selectedCity) {
            loadAdminLayer(selectedCity);
        } else {
            showAlert('请先选择省份或城市', 'warning');
        }
    } else if (mapLayers.admin) {
        map.removeLayer(mapLayers.admin);
        mapLayers.admin = null;
        updateActiveLayersCount();
    }
}

function toggleTerrainLayer(show) {
    if (show && currentCity) {
        loadTerrainLayer(currentCity);
    } else if (mapLayers.terrain) {
        map.removeLayer(mapLayers.terrain);
        mapLayers.terrain = null;
        updateActiveLayersCount();
        // 如果没有其他图层显示，清除图例
        const hasActiveLayers = Object.values(mapLayers).some(layer => layer !== null);
        if (!hasActiveLayers) {
            $('#legendContent').html('<p class="text-muted text-center">请选择图层类型</p>');
        }
    }
}

function switchBaseMap(baseMapType) {
    // 移除当前底图
    map.removeLayer(baseMapLayers[currentBaseMap]);
    
    // 添加新底图
    currentBaseMap = baseMapType;
    baseMapLayers[currentBaseMap].addTo(map);
}

function updateLayerOpacity(layerType, value) {
    // 如果传入的是百分比值，转换为小数
    const opacity = (typeof value === 'string' || value > 1) ? value / 100 : value;

    console.log(`更新${layerType}图层透明度:`, value + (typeof value === 'string' || value > 1 ? '%' : '')); // 调试日志

    // 更新显示的百分比值
    const opacityValueElement = document.getElementById(`${layerType}OpacityValue`);
    if (opacityValueElement) {
        const displayValue = (typeof value === 'string' || value > 1) ? value : Math.round(value * 100);
        opacityValueElement.textContent = displayValue + '%';
    }

    if (mapLayers[layerType]) {
        try {
            if (layerType === 'heatmap') {
                // 热力图层特殊处理
                if (typeof heatmapLayer !== 'undefined' && heatmapLayer) {
                    heatmapLayer.setOptions({opacity: opacity});
                } else if (mapLayers.heatmap.setOptions) {
                    mapLayers.heatmap.setOptions({
                        gradient: {0.4: 'blue', 0.65: 'lime', 1: 'red'},
                        maxOpacity: opacity
                    });
                }
                console.log('热力图透明度已更新:', opacity);
            } else if (layerType === 'terrain' && mapLayers.terrain.setOpacity) {
                // 对于瓦片图层，使用setOpacity方法
                mapLayers.terrain.setOpacity(opacity);
                console.log('地形图层透明度已更新:', opacity);
            } else if (layerType === 'riskBoundaries') {
                // 风险区划图层特殊处理
                const layer = mapLayers.riskBoundaries;
                if (layer.eachLayer && typeof layer.eachLayer === 'function') {
                    layer.eachLayer(function(subLayer) {
                        if (subLayer.setStyle) {
                            // 保持原有的填充颜色，只更新透明度
                            const currentStyle = subLayer.options;
                            subLayer.setStyle({
                                fillOpacity: opacity,
                                opacity: 1  // 边框保持不透明
                            });
                        }
                    });
                }
                console.log('风险区划图层透明度已更新:', opacity);
            } else {
                // 对于其他图层（risk, admin, landuse等）
                const layer = mapLayers[layerType];
                if (layer.eachLayer && typeof layer.eachLayer === 'function') {
                    layer.eachLayer(function(subLayer) {
                        if (subLayer.setStyle) {
                            subLayer.setStyle({
                                fillOpacity: opacity,
                                opacity: opacity
                            });
                        }
                    });
                } else if (layer.setOpacity && typeof layer.setOpacity === 'function') {
                    layer.setOpacity(opacity);
                } else if (layer.setStyle && typeof layer.setStyle === 'function') {
                    layer.setStyle({
                        opacity: opacity,
                        fillOpacity: opacity * 0.8
                    });
                }
                console.log(`${layerType}图层透明度已更新:`, opacity);
            }
        } catch (error) {
            console.error(`更新${layerType}图层透明度时出错:`, error);
        }
    } else {
        console.warn(`图层${layerType}不存在或未加载`);
    }
}

function updateStatistics(data) {
    if (!data || data.length === 0) {
        $('#totalPoints, #highRiskCount, #mediumRiskCount').text('--');
        return;
    }
    
    $('#totalPoints').text(data.length);
    
    const riskCounts = data.reduce((acc, point) => {
        acc[point.risk_level] = (acc[point.risk_level] || 0) + 1;
        return acc;
    }, {});
    
    $('#highRiskCount').text(riskCounts.high || 0);
    $('#mediumRiskCount').text(riskCounts.medium || 0);
}

function updateActiveLayersCount() {
    const count = Object.values(mapLayers).filter(layer => layer !== null).length;
    $('#activeLayersCount').text(count);
}

function clearDataLayers() {
    // 只清除数据图层，保留底图
    Object.values(mapLayers).forEach(layer => {
        if (layer) {
            map.removeLayer(layer);
        }
    });

    Object.keys(mapLayers).forEach(key => {
        mapLayers[key] = null;
    });

    // 清除土地利用统计数据
    window.currentLanduseStatistics = null;

    $('#totalPoints, #highRiskCount, #mediumRiskCount').text('--');
    $('#legendContent').html('<p class="text-muted text-center">请选择图层类型</p>');
    updateActiveLayersCount();
}

function clearMap() {
    clearDataLayers();

    currentCity = null;
    currentDistrict = null;
    currentProvince = null;
    currentYear = null;
    map.setView([35.0, 110.0], 5);

    $('#legendContent').html('<p class="text-muted text-center">请选择城市和图层</p>');
    $('#featureDetailsCard').hide();
    $('#districtSelectContainer').hide();
    $('#riskLegendCard').hide();
}

function toggleFullscreen() {
    const mapCard = $('#mapContainer').closest('.card');
    if (mapCard.hasClass('fullscreen-map')) {
        exitFullscreen();
    } else {
        enterFullscreen();
    }
}

function enterFullscreen() {
    const mapCard = $('#mapContainer').closest('.card');
    mapCard.addClass('fullscreen-map');
    $('#fullscreenBtn').html('<i class="fas fa-compress"></i>');

    // 创建全屏退出按钮
    const exitBtn = $(`
        <button id="fullscreenExitBtn" class="btn btn-outline-secondary fullscreen-exit-btn" title="退出全屏">
            <i class="fas fa-times"></i> 退出全屏
        </button>
    `);

    // 添加退出按钮到页面
    $('body').append(exitBtn);

    // 绑定退出按钮事件
    $('#fullscreenExitBtn').on('click', exitFullscreen);

    // 绑定ESC键退出全屏
    $(document).on('keydown.fullscreen', function(e) {
        if (e.key === 'Escape') {
            exitFullscreen();
        }
    });

    setTimeout(() => {
        map.invalidateSize();
    }, 100);

    console.log('进入全屏模式');
}

function exitFullscreen() {
    const mapCard = $('#mapContainer').closest('.card');
    mapCard.removeClass('fullscreen-map');
    $('#fullscreenBtn').html('<i class="fas fa-expand"></i>');

    // 移除全屏退出按钮
    $('#fullscreenExitBtn').remove();

    // 解绑ESC键事件
    $(document).off('keydown.fullscreen');

    setTimeout(() => {
        map.invalidateSize();
    }, 100);

    console.log('退出全屏模式');
}

function refreshMap() {
    const $btn = $('#refreshBtn');
    const originalHtml = $btn.html();

    $btn.html('<i class="fas fa-spinner fa-spin"></i>').prop('disabled', true);

    try {
        // 获取当前活跃的图层类型
        const activeButton = document.querySelector('.layer-switch-btn.active');
        const activeLayer = activeButton ? activeButton.getAttribute('data-layer') : null;
        const selectedProvince = $('#provinceSelect').val();

        console.log('刷新地图 - 当前图层:', activeLayer, '选择的省份:', selectedProvince);

        // 根据当前图层类型刷新相应的内容
        if (activeLayer && selectedProvince) {
            // 重新切换到当前图层，这会触发数据重新加载
            switchToLayer(activeLayer);
            showAlert('地图数据已刷新', 'success');
        } else if (activeLayer === 'interactive') {
            // 交互地图模式，刷新地图视图
            if (map) {
                map.invalidateSize();
                clearAllLayers();
            }
            showAlert('交互地图已刷新', 'success');
        } else {
            showAlert('请先选择省份和图层类型', 'warning');
        }

    } catch (error) {
        console.error('刷新地图失败:', error);
        showAlert('刷新地图失败', 'error');
    }

    setTimeout(() => {
        $btn.html(originalHtml).prop('disabled', false);
    }, 1000);
}

// 测量工具相关变量
let measureTool = {
    active: false,
    mode: 'distance', // 'distance' 或 'area'
    points: [],
    polyline: null,
    polygon: null,
    markers: [],
    rightClickHandler: null
};

function toggleMeasureTool() {
    if (measureTool.active) {
        stopMeasuring();
    } else {
        startMeasuring();
    }
}

function startMeasuring() {
    measureTool.active = true;
    measureTool.points = [];

    // 更新按钮状态
    $('#measureBtn').removeClass('btn-outline-info').addClass('btn-info')
        .html('<i class="fas fa-ruler"></i> 测量中...');

    // 显示测量模式选择
    showMeasureOptions();

    // 绑定地图点击事件
    map.on('click', onMeasureClick);

    // 改变鼠标样式
    $('#mapContainer').css('cursor', 'crosshair');

    showAlert('点击地图开始测量，右键结束', 'info');
}

function stopMeasuring() {
    measureTool.active = false;

    // 更新按钮状态
    $('#measureBtn').removeClass('btn-info').addClass('btn-outline-info')
        .html('<i class="fas fa-ruler"></i>');

    // 解绑地图事件
    map.off('click', onMeasureClick);

    // 解绑右键事件
    if (measureTool.rightClickHandler) {
        map.off('contextmenu', measureTool.rightClickHandler);
        measureTool.rightClickHandler = null;
    }

    // 恢复鼠标样式
    $('#mapContainer').css('cursor', '');

    // 清除测量结果
    clearMeasurement();

    // 隐藏测量选项
    hideMeasureOptions();

    showAlert('测量已停止', 'success');
}

function onMeasureClick(e) {
    if (!measureTool.active) return;

    measureTool.points.push(e.latlng);

    // 添加标记点
    const marker = L.marker(e.latlng, {
        icon: L.divIcon({
            className: 'measure-marker',
            html: `<div style="background: #007bff; color: white; border-radius: 50%; width: 20px; height: 20px; text-align: center; line-height: 20px; font-size: 12px;">${measureTool.points.length}</div>`,
            iconSize: [20, 20]
        })
    }).addTo(map);

    measureTool.markers.push(marker);

    if (measureTool.points.length === 1) {
        // 创建右键事件处理函数
        measureTool.rightClickHandler = function(e) {
            try {
                if (e && e.originalEvent) {
                    e.originalEvent.preventDefault();
                    e.originalEvent.stopPropagation();
                }
                finishMeasuring();
                return false;
            } catch (error) {
                console.error('右键事件处理错误:', error);
                return false;
            }
        };

        // 绑定右键结束测量
        map.on('contextmenu', measureTool.rightClickHandler);
    } else {
        updateMeasurement();
    }
}

function updateMeasurement() {
    if (measureTool.points.length < 2) return;

    // 清除之前的测量线/面
    if (measureTool.polyline) {
        map.removeLayer(measureTool.polyline);
    }
    if (measureTool.polygon) {
        map.removeLayer(measureTool.polygon);
    }

    if (measureTool.mode === 'distance') {
        // 距离测量
        measureTool.polyline = L.polyline(measureTool.points, {
            color: '#007bff',
            weight: 3,
            opacity: 0.8
        }).addTo(map);

        const distance = calculateDistance();
        showMeasureResult(`距离: ${distance}`);

    } else if (measureTool.mode === 'area') {
        // 面积测量
        if (measureTool.points.length >= 3) {
            measureTool.polygon = L.polygon(measureTool.points, {
                color: '#007bff',
                fillColor: '#007bff',
                fillOpacity: 0.2,
                weight: 3
            }).addTo(map);

            const area = calculateArea();
            showMeasureResult(`面积: ${area}`);
        }
    }
}

function calculateDistance() {
    let totalDistance = 0;
    for (let i = 1; i < measureTool.points.length; i++) {
        totalDistance += measureTool.points[i-1].distanceTo(measureTool.points[i]);
    }

    if (totalDistance < 1000) {
        return `${totalDistance.toFixed(2)} 米`;
    } else {
        return `${(totalDistance / 1000).toFixed(2)} 公里`;
    }
}

function calculateArea() {
    if (measureTool.points.length < 3) return '0 平方米';

    // 使用简单的多边形面积计算（适用于小范围测量）
    let area = 0;
    const points = measureTool.points;

    // 将经纬度转换为近似的米制坐标进行计算
    const centerLat = points.reduce((sum, p) => sum + p.lat, 0) / points.length;
    const meterPerDegLat = 111320; // 纬度1度约等于111320米
    const meterPerDegLng = 111320 * Math.cos(centerLat * Math.PI / 180); // 经度1度的米数随纬度变化

    // 转换为米制坐标
    const meterPoints = points.map(p => ({
        x: (p.lng - points[0].lng) * meterPerDegLng,
        y: (p.lat - points[0].lat) * meterPerDegLat
    }));

    // 使用鞋带公式计算面积
    for (let i = 0; i < meterPoints.length; i++) {
        const j = (i + 1) % meterPoints.length;
        area += meterPoints[i].x * meterPoints[j].y;
        area -= meterPoints[j].x * meterPoints[i].y;
    }
    area = Math.abs(area) / 2;

    if (area < 10000) {
        return `${area.toFixed(2)} 平方米`;
    } else {
        return `${(area / 10000).toFixed(2)} 公顷`;
    }
}

function finishMeasuring() {
    try {
        if (measureTool.points.length >= 2) {
            updateMeasurement();
            showAlert('测量完成！点击"测量"按钮可以清除结果', 'success');
        }

        // 解绑右键事件
        if (measureTool.rightClickHandler) {
            map.off('contextmenu', measureTool.rightClickHandler);
            measureTool.rightClickHandler = null;
        }

        return false;
    } catch (error) {
        console.error('完成测量时发生错误:', error);
        return false;
    }
}

function clearMeasurement() {
    // 清除所有测量元素
    measureTool.markers.forEach(marker => map.removeLayer(marker));
    if (measureTool.polyline) map.removeLayer(measureTool.polyline);
    if (measureTool.polygon) map.removeLayer(measureTool.polygon);

    // 解绑右键事件
    if (measureTool.rightClickHandler) {
        map.off('contextmenu', measureTool.rightClickHandler);
        measureTool.rightClickHandler = null;
    }

    measureTool.points = [];
    measureTool.markers = [];
    measureTool.polyline = null;
    measureTool.polygon = null;

    hideMeasureResult();
}

function showMeasureOptions() {
    const optionsHtml = `
        <div id="measureOptions" style="position: absolute; top: 10px; left: 10px; z-index: 1000; background: white; padding: 10px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div class="btn-group btn-group-sm" role="group">
                <input type="radio" class="btn-check" name="measureMode" id="distanceMode" value="distance" checked>
                <label class="btn btn-outline-primary" for="distanceMode">距离</label>

                <input type="radio" class="btn-check" name="measureMode" id="areaMode" value="area">
                <label class="btn btn-outline-primary" for="areaMode">面积</label>
            </div>
            <button class="btn btn-sm btn-outline-danger ms-2" onclick="clearMeasurement()">清除</button>
        </div>
    `;

    $('#mapContainer').append(optionsHtml);

    // 绑定模式切换事件
    $('input[name="measureMode"]').on('change', function() {
        measureTool.mode = $(this).val();
        clearMeasurement();
    });
}

function hideMeasureOptions() {
    $('#measureOptions').remove();
}

function showMeasureResult(result) {
    // 移除之前的结果
    $('#measureResult').remove();

    const resultHtml = `
        <div id="measureResult" style="position: absolute; top: 60px; left: 10px; z-index: 1000; background: rgba(0,123,255,0.9); color: white; padding: 8px 12px; border-radius: 5px; font-weight: bold;">
            ${result}
        </div>
    `;

    $('#mapContainer').append(resultHtml);
}

function hideMeasureResult() {
    $('#measureResult').remove();
}

function zoomToLayer(layerIndex) {
    const layerNames = ['heatmap', 'risk', 'admin', 'landuse'];
    const layerId = layerNames[layerIndex];
    const layer = mapLayers[layerId];

    if (layer) {
        try {
            const group = new L.featureGroup();
            layer.eachLayer(function(l) {
                group.addLayer(l);
            });

            if (group.getLayers().length > 0) {
                map.fitBounds(group.getBounds(), { padding: [20, 20] });
                showAlert(`已缩放到${layerId}图层`, 'success');
            } else {
                showAlert('图层为空，无法缩放', 'warning');
            }
        } catch (error) {
            console.error('缩放到图层失败:', error);
            showAlert('缩放失败，请稍后重试', 'error');
        }
    } else {
        showAlert('图层未加载', 'warning');
    }
}

function showMapLoading(message) {
    // 先清除之前的加载状态
    $('.map-loading').remove();

    const loadingHtml = `
        <div class="map-loading">
            <div class="text-center">
                <div class="spinner-border text-primary mb-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mb-0">${message}</p>
            </div>
        </div>
    `;
    $('#mapContainer').append(loadingHtml);
    console.log('🔄 显示加载状态:', message);
}

function hideMapLoading() {
    const loadingElements = $('.map-loading');
    if (loadingElements.length > 0) {
        console.log('✅ 隐藏加载状态，清除', loadingElements.length, '个加载元素');
        loadingElements.remove();
    } else {
        console.log('ℹ️ 没有找到需要清除的加载状态元素');
    }
}

function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 10000; min-width: 300px;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('body').append(alertHtml);
    
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 3000);
}

// 新图层切换函数
function toggleHeatmapLayer(show) {
    if (show) {
        // 优先加载省份数据，如果没有省份则加载城市数据
        if (currentProvince) {
            loadHeatmapLayer(null, currentProvince);
        } else if (currentCity) {
            loadHeatmapLayer(currentCity);
        }
    } else if (mapLayers.heatmap) {
        map.removeLayer(mapLayers.heatmap);
        mapLayers.heatmap = null;
    }
}

function toggleLanduseLayer(show) {
    if (show) {
        // 优先使用省份，其次使用城市
        const selectedProvince = $('#provinceSelect').val();
        const selectedCity = currentCity;

        if (selectedProvince && selectedProvince !== '全国') {
            loadLanduseLayer(null, selectedProvince);
        } else if (selectedCity) {
            loadLanduseLayer(selectedCity);
        } else {
            showAlert('请先选择省份或城市', 'warning');
        }
    } else if (mapLayers.landuse) {
        map.removeLayer(mapLayers.landuse);
        mapLayers.landuse = null;
    }
}

function toggleTerrainLayer(show) {
    if (show && currentCity) {
        loadTerrainLayer(currentCity);
    } else if (mapLayers.terrain) {
        map.removeLayer(mapLayers.terrain);
        mapLayers.terrain = null;
    }
}


// 行政区划图层加载（支持城市和省份）
function loadAdminLayer(cityId, provinceId = null) {
    // 构建API URL
    let apiUrl = '/visualization/api/administrative-layers';
    const params = new URLSearchParams();

    if (provinceId) {
        params.append('province', provinceId);
    } else if (cityId) {
        params.append('city', cityId);
    }

    if (params.toString()) {
        apiUrl += '?' + params.toString();
    }

    console.log('加载行政区划图层:', apiUrl);

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data && data.data.length > 0) {
                // 移除现有行政区划图层
                if (mapLayers.admin) {
                    map.removeLayer(mapLayers.admin);
                }

                // 创建图层组
                mapLayers.admin = L.layerGroup();

                // 添加每个行政区域
                data.data.forEach(district => {
                    const polygon = L.polygon(district.coordinates, {
                        color: '#28a745',
                        fillColor: '#28a745',
                        fillOpacity: 0.1,
                        weight: 2
                    }).bindPopup(`
                        <strong>${district.name}</strong><br>
                        人口：${district.population ? district.population.toLocaleString() : '未知'}人<br>
                        面积：${district.area_km2 || '未知'} km²<br>
                        行政级别：${district.admin_level || '未知'}<br>
                        数据来源：${district.source === 'uploaded_data' ? '上传数据' : '系统数据'}
                    `);

                    mapLayers.admin.addLayer(polygon);
                });

                mapLayers.admin.addTo(map);

                // 设置初始透明度
                const opacity = 0.8; // 默认透明度80%
                updateLayerOpacity('admin', opacity);

                // 显示成功消息
                const locationText = provinceId || cityId || '当前区域';
                showAlert(`已加载 ${locationText} 行政区划图层，共 ${data.data.length} 个区域`, 'success');

                updateActiveLayersCount();
            } else {
                console.warn('行政区划图层数据为空:', data);
                const locationText = provinceId || cityId || '当前区域';
                showAlert(data.error || `暂无 ${locationText} 的行政区划数据`, 'warning');
            }
        })
        .catch(error => {
            console.error('加载行政区划图层失败:', error);
            showAlert('加载行政区划图层失败', 'error');
        });
}

// 土地利用图层加载（支持城市和省份）
function loadLanduseLayer(cityId, provinceId = null) {
    // 构建API URL
    let apiUrl = '/visualization/api/landuse-layers';
    const params = new URLSearchParams();

    if (provinceId) {
        params.append('province', provinceId);
    } else if (cityId) {
        params.append('city', cityId);
    }

    if (params.toString()) {
        apiUrl += '?' + params.toString();
    }

    console.log('加载土地利用图层:', apiUrl);

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data && data.data.length > 0) {
                // 移除现有土地利用图层
                if (mapLayers.landuse) {
                    map.removeLayer(mapLayers.landuse);
                }

                // 创建图层组
                mapLayers.landuse = L.layerGroup();

                // 添加每个土地利用区域
                data.data.forEach(area => {
                    const polygon = L.polygon(area.coordinates, {
                        color: area.color,
                        fillColor: area.color,
                        fillOpacity: 0.6,
                        weight: 2
                    }).bindPopup(`
                        <strong>${area.landuse_type}</strong><br>
                        区域：${area.district}<br>
                        面积：${area.area_km2} km²<br>
                        描述：${area.description}<br>
                        数据来源：${area.source === 'uploaded_data' ? '上传数据' : '系统数据'}
                    `);

                    mapLayers.landuse.addLayer(polygon);
                });

                mapLayers.landuse.addTo(map);

                // 设置初始透明度
                const opacity = 0.7; // 默认透明度70%
                updateLayerOpacity('landuse', opacity);

                // 显示成功消息
                const locationText = provinceId || cityId || '当前区域';
                showAlert(`✅ 成功加载 ${locationText} 的土地利用数据 (${data.data.length} 个区域)`, 'success');
            } else {
                // 没有数据的情况
                const locationText = provinceId || cityId || '当前区域';
                const errorMsg = data.error || `暂无 ${locationText} 的土地利用数据`;
                console.warn('土地利用数据为空:', errorMsg);
                showAlert(`⚠️ ${errorMsg}`, 'warning');

                // 清除现有图层
                if (mapLayers.landuse) {
                    map.removeLayer(mapLayers.landuse);
                    mapLayers.landuse = null;
                }
            }
        })
        .catch(error => {
            console.error('加载土地利用图层失败:', error);
            const locationText = provinceId || cityId || '当前区域';
            showAlert(`❌ 加载 ${locationText} 的土地利用图层失败，请检查网络连接或稍后重试`, 'danger');
        });
}

// 地图类型切换功能
function switchMapType(mapType) {
    const leafletMap = document.getElementById('leafletMap');
    const pyechartsMap = document.getElementById('pyechartsMap');

    if (mapType === 'pyecharts') {
        // 切换到PyEcharts地图
        leafletMap.style.display = 'none';
        pyechartsMap.style.display = 'block';

        // 如果当前选择了省份，自动生成PyEcharts地图
        const selectedProvince = $('#provinceSelect').val();
        if (selectedProvince) {
            // 如果没有年份数据，先获取最新年份
            if (!currentYear) {
                loadLatestYearForPyEcharts(selectedProvince);
            } else {
                generatePyEchartsMap(selectedProvince, currentYear);
            }
        } else {
            showPyEchartsPlaceholder();
        }

        console.log('切换到PyEcharts地图模式');
    } else {
        // 切换到Leaflet地图
        leafletMap.style.display = 'block';
        pyechartsMap.style.display = 'none';

        // 刷新Leaflet地图大小
        setTimeout(() => {
            if (map) {
                map.invalidateSize();
            }
        }, 100);

        console.log('切换到Leaflet地图模式');
    }
}

// 生成PyEcharts地图
function generatePyEchartsMap(province, year = null) {
    console.log(`生成PyEcharts地图: ${province}, 年份: ${year}`);

    showMapLoading('正在生成风险区划图...');

    // 构建API URL
    let apiUrl = `/visualization/api/generate-pyecharts-map/${encodeURIComponent(province)}`;
    const params = new URLSearchParams();

    // 只有当year不为空字符串且不为null时才添加年份参数
    if (year && year !== '') {
        params.append('year', year);
    }
    params.append('type', 'map'); // 默认使用区域填充图

    if (params.toString()) {
        apiUrl += '?' + params.toString();
    }

    fetch(apiUrl)
        .then(response => {
            console.log('PyEcharts API响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('PyEcharts API响应数据:', data);
            if (data.success) {
                console.log('地图URL:', data.map_url);

                // 测试文件是否可访问
                testFileAccess(data.map_url).then(accessible => {
                    if (accessible) {
                        displayPyEchartsMap(data.map_url);
                        updatePyEchartsMapInfo(data);
                        showAlert(`${province} 风险区划图生成成功`, 'success');
                        $('#riskLegendCard').show();
                    } else {
                        console.error('地图文件无法访问:', data.map_url);
                        showAlert('地图文件生成成功但无法访问，请检查文件路径', 'warning');
                        showPyEchartsPlaceholder();
                    }
                });
            } else {
                console.error('PyEcharts地图生成失败:', data.error);

                // 如果是年份相关的错误，提供更友好的提示
                if (data.error && data.error.includes('年的风险评估数据')) {
                    showAlert(`${data.error}，请选择其他年份`, 'warning');
                } else {
                    showAlert(`风险区划图生成失败: ${data.error}`, 'error');
                }
                showPyEchartsPlaceholder();
            }
        })
        .catch(error => {
            console.error('生成PyEcharts地图失败:', error);
            showAlert(`风险区划图生成失败: ${error.message}`, 'error');
            showPyEchartsPlaceholder();
        })
        .finally(() => {
            // 不在这里隐藏加载状态，让iframe的onload事件处理
        });
}

// 检查土地利用数据可用性
function checkLanduseDataAvailability(province) {
    return new Promise((resolve, reject) => {
        console.log(`🔍 检查 ${province} 的土地利用数据可用性...`);

        const apiUrl = `/visualization/api/province-landuse-boundaries/${encodeURIComponent(province)}`;

        fetch(apiUrl)
            .then(response => response.json())
            .then(data => {
                console.log('土地利用数据检查结果:', data);
                resolve(data.success && data.feature_count > 0);
            })
            .catch(error => {
                console.error('检查土地利用数据失败:', error);
                reject(error);
            });
    });
}

// 生成PyEcharts土地利用地图（直接使用PNG图片版本）
function generatePyEchartsLanduseMap(province) {
    console.log(`🌱 加载土地利用PNG图层: ${province}`);

    showMapLoading('正在加载土地利用PNG图层...');

    // 使用PNG图层API
    const apiUrl = `/visualization/api/landuse-png-layer/${encodeURIComponent(province)}`;

    fetch(apiUrl)
        .then(response => {
            console.log('PNG图层API响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('PNG图层API响应数据:', data);

            if (data.success && data.image_url) {
                console.log('✅ PNG图层数据获取成功');

                // 直接显示PNG图片作为主要内容
                displayPngAsMainContent(data.image_url, data.bounds, province, data.layer_name);

                // 如果有统计信息，显示动态图例
                if (data.statistics) {
                    console.log('📊 显示统计信息和动态图例');
                    displayDynamicLanduseLegend(data.statistics);
                    displayLanduseStatistics(data.statistics, province);
                }

                // 显示成功消息
                showAlert(`成功加载 ${province} 土地利用PNG图层`, 'success');
            } else {
                console.error('❌ PNG图层获取失败:', data.error || '未知错误');
                showPyEchartsPlaceholder(data.error || 'PNG图层获取失败', 'landuse');
                showAlert(data.error || 'PNG图层获取失败', 'error');
            }
        })
        .catch(error => {
            console.error('❌ PNG图层请求失败:', error);
            console.error('请求URL:', apiUrl);
            console.error('错误详情:', error.message);

            let errorMessage = '网络错误，请稍后重试';
            if (error.message.includes('404')) {
                errorMessage = `未找到 ${province} 的土地利用数据，请先上传相关数据文件`;
            } else if (error.message.includes('500')) {
                errorMessage = '服务器处理错误，请检查数据文件格式';
            }

            showPyEchartsPlaceholder(errorMessage, 'landuse');
            showAlert(errorMessage, 'error');
        })
        .finally(() => {
            hideMapLoading();
        });
}

// 将PNG图片作为主要内容显示（简化版本，支持缩放）
function displayPngAsMainContent(imageUrl, bounds, province, layerName) {
    console.log('📸 显示PNG图片作为主要内容:', imageUrl);

    const mapContainer = document.getElementById('pyechartsMap');
    if (!mapContainer) {
        console.error('地图容器不存在');
        return;
    }

    // 清空容器
    mapContainer.innerHTML = '';

    // 创建PNG图片显示区域（全屏，支持缩放）
    const pngContainer = document.createElement('div');
    pngContainer.className = 'png-landuse-container';
    pngContainer.style.cssText = `
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #ffffff;
        overflow: hidden;
        position: relative;
    `;

    // 创建可缩放的图片容器
    const zoomContainer = document.createElement('div');
    zoomContainer.className = 'zoom-container';
    zoomContainer.style.cssText = `
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        cursor: grab;
    `;

    const img = document.createElement('img');
    img.src = imageUrl;
    img.alt = `${province} 土地利用分布图`;
    img.style.cssText = `
        max-width: 100%;
        max-height: 100%;
        user-select: none;
        pointer-events: none;
        will-change: transform;
        backface-visibility: hidden;
        transform-origin: center center;
    `;

    // 缩放和拖拽变量
    let scale = 1;
    let isDragging = false;
    let startX = 0;
    let startY = 0;
    let translateX = 0;
    let translateY = 0;

    // 更新图片变换（优化性能）
    function updateTransform() {
        // 使用translate3d启用硬件加速，提高性能
        img.style.transform = `translate3d(${translateX}px, ${translateY}px, 0) scale(${scale})`;
        img.style.transformOrigin = 'center center';
    }

    // 鼠标滚轮缩放（优化性能，保持居中）
    let zoomTimeout;
    zoomContainer.addEventListener('wheel', (e) => {
        e.preventDefault();

        const deltaScale = e.deltaY > 0 ? 0.9 : 1.1;
        const newScale = Math.max(0.1, Math.min(5, scale * deltaScale));

        if (newScale !== scale) {
            scale = newScale;

            // 缩放时保持图片居中，不改变translateX和translateY
            // 只有在拖拽时才移动位置

            // 使用requestAnimationFrame优化性能
            if (zoomTimeout) {
                cancelAnimationFrame(zoomTimeout);
            }

            zoomTimeout = requestAnimationFrame(() => {
                updateTransform();
            });
        }
    });

    // 鼠标拖拽（优化性能）
    let dragTimeout;

    zoomContainer.addEventListener('mousedown', (e) => {
        if (scale > 1) {
            isDragging = true;
            startX = e.clientX - translateX;
            startY = e.clientY - translateY;
            zoomContainer.style.cursor = 'grabbing';
            e.preventDefault();
        }
    });

    document.addEventListener('mousemove', (e) => {
        if (isDragging) {
            translateX = e.clientX - startX;
            translateY = e.clientY - startY;

            // 使用requestAnimationFrame节流，提高性能
            if (dragTimeout) {
                cancelAnimationFrame(dragTimeout);
            }

            dragTimeout = requestAnimationFrame(() => {
                updateTransform();
            });
        }
    });

    document.addEventListener('mouseup', () => {
        isDragging = false;
        zoomContainer.style.cursor = scale > 1 ? 'grab' : 'default';
    });

    // 双击重置
    zoomContainer.addEventListener('dblclick', () => {
        scale = 1;
        translateX = 0;
        translateY = 0;
        updateTransform();
        zoomContainer.style.cursor = 'default';
    });

    zoomContainer.appendChild(img);
    pngContainer.appendChild(zoomContainer);
    mapContainer.appendChild(pngContainer);

    console.log('✅ PNG图片已显示，支持滚轮缩放和拖拽');
}

// 在新标签页中打开图片
function openImageInNewTab(imageUrl) {
    window.open(imageUrl, '_blank');
}

// 切换到地图视图
function switchToMapView(province) {
    console.log('🗺️ 切换到地图视图:', province);

    // 使用完整数据API获取地图
    const apiUrl = `/visualization/api/landuse-data/${encodeURIComponent(province)}`;

    showMapLoading('正在加载地图视图...');

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.map_url) {
                displayPyEchartsMap(data.map_url);

                // 显示PNG图片预览
                if (data.image_url) {
                    displayImagePreview(data.image_url);
                }

                // 显示统计信息
                if (data.statistics) {
                    displayLanduseStatistics(data.statistics, province);
                }

                showAlert('已切换到地图视图', 'success');
            } else {
                showAlert('切换到地图视图失败', 'error');
            }
        })
        .catch(error => {
            console.error('切换到地图视图失败:', error);
            showAlert('切换到地图视图失败', 'error');
        })
        .finally(() => {
            hideMapLoading();
        });
}

// 显示土地利用TIFF图片并添加到地图图层
function displayLanduseImage(imageUrl, statistics, bounds) {
    console.log('📸 显示土地利用TIFF图片:', imageUrl);
    console.log('📍 图片边界:', bounds);
    console.log('📊 统计信息:', statistics);

    // 1. 在右上角显示图片预览
    displayImagePreview(imageUrl);

    // 2. 如果有边界信息且当前是Leaflet地图，添加PNG图层叠加
    if (bounds && window.currentMap && typeof window.currentMap.addLayer === 'function') {
        addPngLayerToMap(imageUrl, bounds);
    }

    // 3. 如果有统计信息，显示动态图例
    if (statistics) {
        displayDynamicLanduseLegend(statistics);

        // 显示土地利用图例卡片
        const legendCard = document.getElementById('landuseLegendCard');
        if (legendCard) {
            legendCard.style.display = 'block';
        }
    }
}

// 显示动态土地利用图例
function displayDynamicLanduseLegend(statistics) {
    console.log('🎨 显示动态土地利用图例:', statistics);

    // 更新全局统计数据
    window.currentLanduseStatistics = statistics;

    // 更新通用图例容器
    const legendContent = document.getElementById('legendContent');
    if (legendContent) {
        // 清空现有内容
        legendContent.innerHTML = '';

        // 添加标题
        const titleElement = document.createElement('h6');
        titleElement.className = 'mb-2';
        titleElement.textContent = '土地利用类型';
        legendContent.appendChild(titleElement);

        // 添加动态图例
        addLanduseLegend(legendContent, statistics);
    }

    // 同时更新专用的土地利用图例卡片
    const legendCard = document.getElementById('landuseLegendCard');
    if (legendCard) {
        const legendCardContent = legendCard.querySelector('.legend-content');
        if (legendCardContent) {
            displayLanduseStatistics(statistics, '当前区域');
        }
    }
}

// 显示图片预览窗口
function displayImagePreview(imageUrl) {
    // 创建图片显示区域
    const imageContainer = document.getElementById('landuseImageContainer');
    if (!imageContainer) {
        // 如果容器不存在，创建一个
        const container = document.createElement('div');
        container.id = 'landuseImageContainer';
        container.className = 'landuse-image-container';
        container.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            display: none;
        `;

        document.getElementById('pyechartsMap').appendChild(container);
    }

    // 更新图片内容
    const container = document.getElementById('landuseImageContainer');
    container.innerHTML = `
        <div style="padding: 10px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <h6 style="margin: 0; font-size: 14px;">土地利用分布图</h6>
                <div>
                    <button onclick="togglePngLayer()" style="border: none; background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-right: 5px; cursor: pointer;">图层</button>
                    <button onclick="toggleLanduseImage()" style="border: none; background: none; font-size: 16px; cursor: pointer;">×</button>
                </div>
            </div>
            <img src="${imageUrl}" alt="土地利用分布图" style="width: 100%; border-radius: 4px;">
            <div style="margin-top: 8px; font-size: 12px; color: #666;">
                点击图片可查看大图，点击"图层"按钮可在地图上显示/隐藏PNG图层
            </div>
        </div>
    `;

    // 添加点击事件
    const img = container.querySelector('img');
    img.onclick = () => {
        window.open(imageUrl, '_blank');
    };
    img.style.cursor = 'pointer';

    // 显示容器
    container.style.display = 'block';
}

// 添加PNG图层到地图
function addPngLayerToMap(imageUrl, bounds) {
    console.log('🗺️ 添加PNG图层到地图:', imageUrl, bounds);

    try {
        // 检查是否为Leaflet地图
        if (window.currentMap && typeof window.currentMap.addLayer === 'function') {
            // 移除之前的PNG图层
            if (window.currentPngLayer) {
                window.currentMap.removeLayer(window.currentPngLayer);
            }

            // 创建图片叠加层
            // bounds格式: [south, west, north, east]
            const imageBounds = [[bounds[0], bounds[1]], [bounds[2], bounds[3]]];

            window.currentPngLayer = L.imageOverlay(imageUrl, imageBounds, {
                opacity: 0.7,
                interactive: false
            });

            // 添加到地图
            window.currentPngLayer.addTo(window.currentMap);

            // 调整地图视图到图片边界
            window.currentMap.fitBounds(imageBounds);

            console.log('✅ PNG图层添加成功');
            showAlert('PNG土地利用图层已添加到地图', 'success');
        } else {
            console.log('ℹ️ 当前不是Leaflet地图，跳过PNG图层叠加');
        }
    } catch (error) {
        console.error('❌ 添加PNG图层失败:', error);
        showAlert('添加PNG图层失败', 'error');
    }
}

// 切换PNG图层显示/隐藏
function togglePngLayer() {
    if (window.currentPngLayer && window.currentMap) {
        if (window.currentMap.hasLayer(window.currentPngLayer)) {
            window.currentMap.removeLayer(window.currentPngLayer);
            showAlert('PNG图层已隐藏', 'info');
        } else {
            window.currentPngLayer.addTo(window.currentMap);
            showAlert('PNG图层已显示', 'success');
        }
    } else {
        showAlert('PNG图层不可用', 'warning');
    }
}

// 切换土地利用图片显示
function toggleLanduseImage() {
    const container = document.getElementById('landuseImageContainer');
    if (container) {
        container.style.display = container.style.display === 'none' ? 'block' : 'none';
    }
}

// 显示土地利用统计信息
function displayLanduseStatistics(statistics, province) {
    console.log('📊 显示土地利用统计信息:', statistics);

    // 更新图例卡片
    const legendCard = document.getElementById('landuseLegendCard');
    if (legendCard) {
        const legendContent = legendCard.querySelector('.legend-content');
        if (legendContent) {
            let html = `<h6>${province} 土地利用统计</h6>`;

            const landuse_types = statistics.landuse_types || {};
            const total_pixels = statistics.total_pixels || 0;
            const dominant_type = statistics.dominant_type || '未知';

            html += `<div style="margin-bottom: 10px; font-size: 12px;">
                <div>总像素数: ${total_pixels.toLocaleString()}</div>
                <div>主导类型: ${dominant_type}</div>
                <div>类型数量: ${Object.keys(landuse_types).length}</div>
            </div>`;

            // 动态生成图例，根据实际数据内容
            html += '<div class="landuse-legend" style="margin-bottom: 10px;">';
            html += '<h6 style="font-size: 14px; margin-bottom: 8px;">图例</h6>';

            // 获取土地利用类型颜色映射
            const colorMapping = getLanduseColorMapping();

            for (const [typeName, info] of Object.entries(landuse_types)) {
                const percentage = info.percentage || 0;
                if (percentage >= 0.1) { // 只显示占比大于0.1%的类型
                    const color = colorMapping[typeName] || '#DDA0DD'; // 默认颜色
                    html += `
                        <div style="display: flex; align-items: center; margin-bottom: 4px; font-size: 12px;">
                            <div style="width: 16px; height: 16px; background-color: ${color}; margin-right: 8px; border-radius: 3px; border: 1px solid #ccc;"></div>
                            <span style="flex: 1;">${typeName}</span>
                            <span style="font-weight: bold;">${percentage}%</span>
                        </div>
                    `;
                }
            }
            html += '</div>';

            // 显示详细统计
            html += '<div class="landuse-details" style="font-size: 11px; color: #666;">';
            html += '<div style="margin-bottom: 4px;"><strong>详细统计:</strong></div>';
            for (const [typeName, info] of Object.entries(landuse_types)) {
                const percentage = info.percentage || 0;
                const pixels = info.pixels || 0;
                if (percentage >= 0.1) {
                    html += `
                        <div style="margin-bottom: 2px;">
                            ${typeName}: ${pixels.toLocaleString()} 像素 (${percentage}%)
                        </div>
                    `;
                }
            }
            html += '</div>';

            legendContent.innerHTML = html;
        }
    }
}

// 获取土地利用类型颜色映射
function getLanduseColorMapping() {
    return {
        '耕地': '#FFD700',      // 金色
        '林地': '#228B22',      // 森林绿
        '草地': '#90EE90',      // 浅绿色
        '灌木地': '#9ACD32',    // 黄绿色
        '湿地': '#20B2AA',      // 浅海绿
        '水域': '#4169E1',      // 皇家蓝
        '苔原': '#DDA0DD',      // 梅花色
        '建设用地': '#808080',   // 灰色
        '裸地': '#D2B48C',      // 棕褐色
        '冰雪': '#F0F8FF',      // 爱丽丝蓝
        '未利用地': '#D2B48C',   // 棕褐色
        '住宅用地': '#FFB6C1',   // 浅粉色
        '商业用地': '#FF6347',   // 番茄红
        '工业用地': '#8B4513',   // 马鞍棕
        '交通用地': '#696969',   // 暗灰色
        '其他': '#DDA0DD'       // 梅花色
    };
}

// 显示PyEcharts地图
function displayPyEchartsMap(mapUrl) {
    console.log('🗺️ 开始显示PyEcharts地图:', mapUrl);

    // 立即清除加载状态
    hideMapLoading();

    // 确保PyEcharts地图容器可见
    const leafletMap = document.getElementById('leafletMap');
    const pyechartsMap = document.getElementById('pyechartsMap');
    const iframe = document.getElementById('pyechartsFrame');

    if (!iframe) {
        console.error('❌ PyEcharts iframe元素不存在');
        showAlert('地图容器初始化失败', 'error');
        return;
    }

    // 清除iframe的srcdoc属性，确保使用src属性
    iframe.removeAttribute('srcdoc');

    // 切换地图显示
    leafletMap.style.display = 'none';
    pyechartsMap.style.display = 'block';
    console.log('✅ 已切换到PyEcharts地图显示模式');

    // 使用专门的PyEcharts地图服务路由
    let finalUrl;

    if (mapUrl && mapUrl.startsWith('maps/')) {
        // 如果是maps/开头的路径，提取文件名并使用专门的服务路由
        const filename = mapUrl.replace('maps/', '');
        finalUrl = `/visualization/api/pyecharts-map/${filename}`;
    } else if (mapUrl && mapUrl.includes('/')) {
        // 如果包含路径分隔符，提取文件名
        const filename = mapUrl.split('/').pop();
        finalUrl = `/visualization/api/pyecharts-map/${filename}`;
    } else {
        // 直接使用文件名
        finalUrl = `/visualization/api/pyecharts-map/${mapUrl}`;
    }

    // 添加iframe加载事件监听
    iframe.onload = function() {
        console.log('✅ PyEcharts地图iframe加载成功');
        console.log('🔍 当前iframe src:', iframe.src);
        console.log('🔍 iframe contentDocument:', iframe.contentDocument);
        hideMapLoading();

        // 额外的安全检查，确保加载状态被隐藏
        setTimeout(() => {
            const loadingElements = document.querySelectorAll('.map-loading');
            if (loadingElements.length > 0) {
                console.warn('⚠️ 发现残留的加载状态元素，强制清除');
                loadingElements.forEach(el => el.remove());
            }
        }, 1000);
    };

    iframe.onerror = function() {
        console.error('❌ PyEcharts地图iframe加载失败');
        console.error('❌ 失败的URL:', iframe.src);
        showAlert(`地图文件加载失败: ${iframe.src}`, 'error');
        hideMapLoading();
    };

    // 设置iframe源
    console.log('🚀 开始设置iframe源:', finalUrl);
    iframe.src = finalUrl;
    console.log('🔗 PyEcharts地图URL已设置:', finalUrl);
    console.log('📄 原始URL:', mapUrl);

    // 添加超时机制，防止iframe事件不触发导致加载状态一直显示
    setTimeout(() => {
        const loadingElements = document.querySelectorAll('.map-loading');
        if (loadingElements.length > 0) {
            console.warn('⏰ 超时检测：iframe可能加载失败，强制隐藏加载状态');
            hideMapLoading();
        }
    }, 10000); // 10秒超时
}

// 测试文件是否可访问
function testFileAccess(mapUrl) {
    return new Promise((resolve) => {
        // 使用与displayPyEchartsMap相同的URL构建逻辑
        let testUrl;

        if (mapUrl && mapUrl.startsWith('maps/')) {
            const filename = mapUrl.replace('maps/', '');
            testUrl = `/visualization/api/pyecharts-map/${filename}`;
        } else if (mapUrl && mapUrl.includes('/')) {
            const filename = mapUrl.split('/').pop();
            testUrl = `/visualization/api/pyecharts-map/${filename}`;
        } else {
            testUrl = `/visualization/api/pyecharts-map/${mapUrl}`;
        }

        console.log('测试文件访问:', testUrl);

        // 使用fetch测试文件是否可访问
        fetch(testUrl, { method: 'HEAD' })
            .then(response => {
                console.log('文件访问测试结果:', response.status);
                resolve(response.ok);
            })
            .catch(error => {
                console.error('文件访问测试失败:', error);
                resolve(false);
            });
    });
}

// 显示PyEcharts地图占位符
function showPyEchartsPlaceholder(message = null, layerType = 'risk') {
    const iframe = document.getElementById('pyechartsFrame');

    // 根据图层类型设置不同的图标和标题
    let icon, title, defaultMessage;
    switch(layerType) {
        case 'admin':
            icon = 'fas fa-map-marked-alt';
            title = '行政区划图';
            defaultMessage = '请先选择省份来查看行政区划图';
            break;
        case 'landuse':
            icon = 'fas fa-seedling';
            title = '土地利用图';
            defaultMessage = '请先选择省份来查看土地利用图';
            break;
        default:
            icon = 'fas fa-exclamation-triangle';
            title = '风险区划图';
            defaultMessage = '请先选择省份，然后选择年份来查看风险区划图';
    }

    const displayMessage = message || defaultMessage;

    const placeholderHtml = `
        <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%; background-color: #f8f9fa;">
            <i class="${icon} fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">${title}</h5>
            <p class="text-muted text-center">${displayMessage}</p>
            ${layerType === 'risk' ? `
            <div class="text-muted small">
                <i class="fas fa-info-circle me-1"></i>
                步骤：1. 选择省份 → 2. 选择年份 → 3. 查看地图
            </div>
            ` : ''}
        </div>
    `;

    iframe.srcdoc = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        </head>
        <body>
            ${placeholderHtml}
        </body>
        </html>
    `;
}

// 更新PyEcharts地图信息
function updatePyEchartsMapInfo(data) {
    // 更新地图统计信息
    if (data.data_summary) {
        const summary = data.data_summary;
        $('#totalCities').text(summary.total_cities || '--');
        $('#avgRiskIndex').text(((summary.avg_risk_index || 0) * 100).toFixed(1) + '%');
        $('#maxRiskIndex').text(((summary.max_risk_index || 0) * 100).toFixed(1) + '%');
        $('#minRiskIndex').text(((summary.min_risk_index || 0) * 100).toFixed(1) + '%');
    }

    console.log('PyEcharts地图信息已更新:', data);
}

// 通用图层切换函数
function toggleLayer(layerType) {
    const checkbox = document.getElementById(layerType + 'Toggle');
    const isChecked = checkbox.checked;

    console.log(`切换图层: ${layerType}, 状态: ${isChecked}`);

    switch(layerType) {
        case 'heatmap':
            toggleHeatmapLayer(isChecked);
            break;
        case 'flood':
            toggleFloodLayer(isChecked);
            break;
        case 'risk':
            toggleRiskLayer(isChecked);
            break;
        case 'admin':
            toggleAdminLayer(isChecked);
            break;
        case 'terrain':
            toggleTerrainLayer(isChecked);
            break;
        case 'landuse':
            toggleLanduseLayer(isChecked);
            break;
        case 'riskBoundaries':
            toggleRiskBoundariesLayer(isChecked);
            break;
        case 'landuseBoundaries':
            toggleLanduseBoundariesLayer(isChecked);
            break;
        default:
            console.warn(`未知的图层类型: ${layerType}`);
    }
}

// 风险区划图层相关函数
function toggleRiskBoundariesLayer(show) {
    if (show && currentProvince) {
        // 如果当前年份为空，先获取最新年份再加载
        if (!currentYear) {
            loadLatestYearAndRiskBoundaries(currentProvince);
        } else {
            loadRiskBoundariesLayer(currentProvince, currentYear);
        }
    } else if (mapLayers.riskBoundaries) {
        map.removeLayer(mapLayers.riskBoundaries);
        mapLayers.riskBoundaries = null;
        updateActiveLayersCount();
    }
}

// 获取最新年份并加载风险区划图层
function loadLatestYearAndRiskBoundaries(province) {
    console.log(`获取 ${province} 的最新年份数据...`);
    showMapLoading('获取最新年份数据...');

    fetch(`/api/provinces/${encodeURIComponent(province)}/years`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.years && data.years.length > 0) {
                // 设置最新年份
                currentYear = data.years[0].year; // 数据已按年份降序排列

                // 更新年份选择器（如果显示的话）
                if ($('#yearSelectContainer').is(':visible')) {
                    $('#yearSelect').val(currentYear);
                }

                console.log(`已设置最新年份: ${currentYear}`);

                // 加载风险区划图层
                loadRiskBoundariesLayer(province, currentYear);
            } else if (data.success) {
                // API调用成功但没有年份数据
                console.warn(`${province} 暂无年份数据`);
                hideMapLoading();

                const message = data.message || `${province} 暂无风险区划数据，请先导入相关数据`;
                showAlert(message, 'info');

                // 不尝试加载默认数据，因为没有数据
                return;
            } else {
                console.warn('获取年份数据失败，尝试加载默认数据');
                loadRiskBoundariesLayer(province, null);
            }
        })
        .catch(error => {
            console.error('获取年份数据失败:', error);
            hideMapLoading();

            // 根据错误类型显示不同的提示
            let errorMessage;
            if (error.message.includes('404')) {
                errorMessage = `${province} 暂无年份数据，请先导入相关数据`;
            } else {
                errorMessage = '网络连接异常，无法获取年份数据';
            }

            showAlert(errorMessage, 'warning');
        });
}



function loadRiskBoundariesLayer(province, year = null) {
    console.log(`开始加载风险区划图层: ${province}, 年份: ${year}`);
    showMapLoading('加载风险区划图层...');

    // 构建API URL
    let apiUrl = `/visualization/api/province-risk-boundaries/${encodeURIComponent(province)}`;
    if (year) {
        apiUrl += `?year=${year}`;
    }

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            console.log('风险区划图层响应:', data);
            if (data.success) {
                displayRiskBoundariesLayer(data.data, data.province, data.year);

                // 显示成功信息，包含年份
                const yearText = data.year ? `${data.year}年` : '';
                showAlert(`已加载 ${province} ${yearText} 风险区划图层`, 'success');

                // 显示风险等级图例
                $('#riskLegendCard').show();
            } else {
                console.error('风险区划图层加载失败:', data.error);
                showAlert(`风险区划图层加载失败: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('加载风险区划图层失败:', error);
            showAlert(`风险区划图层加载失败: ${error.message}`, 'error');
        })
        .finally(() => {
            hideMapLoading();
        });
}

// 土地利用图层相关函数
function toggleLanduseBoundariesLayer(show) {
    if (show && currentProvince) {
        loadLanduseBoundariesLayer(currentProvince);
    } else if (mapLayers.landuseBoundaries) {
        map.removeLayer(mapLayers.landuseBoundaries);
        mapLayers.landuseBoundaries = null;
        updateActiveLayersCount();
    }
}

function loadLanduseBoundariesLayer(province) {
    console.log(`开始加载土地利用图层: ${province}`);
    showMapLoading('加载土地利用图层...');

    // 构建API URL
    let apiUrl = `/visualization/api/province-landuse-boundaries/${encodeURIComponent(province)}`;

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            console.log('土地利用图层响应:', data);
            if (data.success) {
                displayLanduseBoundariesLayer(data.data, data.province);

                showAlert(`已加载 ${province} 土地利用图层`, 'success');

                // 显示土地利用图例
                $('#landuseLegendCard').show();
            } else {
                console.error('土地利用图层加载失败:', data.error);
                showAlert(`土地利用图层加载失败: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('加载土地利用图层失败:', error);
            showAlert(`土地利用图层加载失败: ${error.message}`, 'error');
        })
        .finally(() => {
            hideMapLoading();
        });
}

function displayLanduseBoundariesLayer(geoJsonData, province) {
    console.log('显示土地利用图层:', geoJsonData);

    // 移除现有的土地利用图层
    if (mapLayers.landuseBoundaries) {
        map.removeLayer(mapLayers.landuseBoundaries);
    }

    // 创建GeoJSON图层
    mapLayers.landuseBoundaries = L.geoJSON(geoJsonData, {
        style: function(feature) {
            const properties = feature.properties;
            return {
                fillColor: properties.fill_color || '#90EE90',
                weight: 2,
                opacity: 1,
                color: '#ffffff',
                fillOpacity: properties.fill_opacity || 0.6
            };
        },
        onEachFeature: function(feature, layer) {
            // 绑定弹窗信息
            layer.bindPopup(`
                <div class="landuse-popup">
                    <h6 class="mb-2">${feature.properties.name}</h6>
                    <div class="landuse-info">
                        <p><strong>土地利用类型:</strong>
                           <span class="badge badge-success">
                               ${feature.properties.landuse_type}
                           </span>
                        </p>
                        <p><strong>面积:</strong> ${feature.properties.area_km2} km²</p>
                        <p><strong>省份:</strong> ${feature.properties.province}</p>
                        <p><strong>描述:</strong> ${feature.properties.description}</p>
                        <p><strong>数据来源:</strong> ${feature.properties.source}</p>
                    </div>
                </div>
            `);

            // 鼠标悬停效果
            layer.on({
                mouseover: function(e) {
                    const layer = e.target;
                    layer.setStyle({
                        weight: 3,
                        color: '#666',
                        dashArray: '',
                        fillOpacity: 0.8
                    });
                    if (!L.Browser.ie && !L.Browser.opera && !L.Browser.edge) {
                        layer.bringToFront();
                    }
                },
                mouseout: function(e) {
                    mapLayers.landuseBoundaries.resetStyle(e.target);
                }
            });
        }
    }).addTo(map);

    updateActiveLayersCount();
    console.log('土地利用图层显示完成');
}

function displayRiskBoundariesLayer(geoJsonData, province, year) {
    console.log('显示风险区划图层:', geoJsonData);

    // 移除现有的风险区划图层
    if (mapLayers.riskBoundaries) {
        map.removeLayer(mapLayers.riskBoundaries);
    }

    // 创建GeoJSON图层
    mapLayers.riskBoundaries = L.geoJSON(geoJsonData, {
        style: function(feature) {
            const properties = feature.properties;
            return {
                fillColor: properties.fill_color || '#6c757d',
                weight: 2,
                opacity: 1,
                color: '#ffffff',
                fillOpacity: properties.fill_opacity || 0.7
            };
        },
        onEachFeature: function(feature, layer) {
            const properties = feature.properties;

            // 创建弹出窗口内容
            const popupContent = `
                <div class="popup-content">
                    <h6 class="mb-2">${properties.name}</h6>
                    <div class="mb-2">
                        <span class="badge" style="background-color: ${properties.fill_color}">
                            ${properties.risk_level}
                        </span>
                    </div>
                    <div class="small">
                        <div><strong>风险指数:</strong> ${(properties.risk_index || 0).toFixed(3)}</div>
                        <div><strong>发生概率:</strong> ${(properties.likelihood_probability || 0).toFixed(3)}</div>
                        <div><strong>影响程度:</strong> ${(properties.impact_degree || 0).toFixed(3)}</div>
                        ${year ? `<div><strong>评估年份:</strong> ${year}年</div>` : ''}
                    </div>
                </div>
            `;

            layer.bindPopup(popupContent);

            // 鼠标悬停效果
            layer.on({
                mouseover: function(e) {
                    const layer = e.target;
                    layer.setStyle({
                        weight: 3,
                        color: '#666',
                        dashArray: '',
                        fillOpacity: 0.9
                    });

                    if (!L.Browser.ie && !L.Browser.opera && !L.Browser.edge) {
                        layer.bringToFront();
                    }
                },
                mouseout: function(e) {
                    mapLayers.riskBoundaries.resetStyle(e.target);
                }
            });
        }
    }).addTo(map);

    updateActiveLayersCount();
    console.log('风险区划图层显示完成');
}

// GIS图层管理功能
let gisLayers = [];

// 加载GIS图层列表
function loadGISLayers() {
    fetch('/api/gis-layers')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                gisLayers = data.data;
                renderGISLayersList();
            } else {
                console.error('加载GIS图层失败:', data.error);
            }
        })
        .catch(error => {
            console.error('加载GIS图层失败:', error);
        });
}

// 渲染GIS图层列表
function renderGISLayersList() {
    const container = document.getElementById('gisLayersList');
    const noLayersMessage = document.getElementById('noGISLayersMessage');

    if (gisLayers.length === 0) {
        noLayersMessage.style.display = 'block';
        return;
    }

    noLayersMessage.style.display = 'none';

    const layersHTML = gisLayers.map(layer => `
        <div class="layer-item gis-layer-item" data-layer-id="${layer.id}">
            <div class="layer-header">
                <span class="layer-name" title="${layer.description || layer.name}">
                    <i class="fas fa-${layer.layer_type === 'vector' ? 'vector-square' : 'image'} me-1"></i>
                    ${layer.display_name || layer.name}
                    <small class="text-muted">(${layer.province || '未知'})</small>
                </span>
                <div class="layer-controls">
                    <div class="form-check form-switch">
                        <input class="form-check-input gis-layer-toggle" type="checkbox"
                               id="gisLayer${layer.id}Toggle"
                               onchange="toggleGISLayer(${layer.id}, this.checked)">
                    </div>
                    <button class="btn btn-sm btn-outline-danger ms-1"
                            onclick="deleteGISLayer(${layer.id})" title="删除图层">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="opacity-control">
                <div class="opacity-label">透明度</div>
                <div class="d-flex align-items-center gap-2">
                    <input type="range" class="form-range opacity-slider" min="0" max="100" value="${layer.opacity * 100}"
                           id="gisLayer${layer.id}Opacity"
                           onchange="updateGISLayerOpacity(${layer.id}, this.value)">
                    <span class="opacity-value" id="gisLayer${layer.id}OpacityValue">${Math.round(layer.opacity * 100)}%</span>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = layersHTML;
}

// 切换GIS图层显示
function toggleGISLayer(layerId, isVisible) {
    const layer = gisLayers.find(l => l.id === layerId);
    if (!layer) return;

    if (isVisible) {
        loadGISLayerOnMap(layer);
    } else {
        removeGISLayerFromMap(layerId);
    }
}

// 在地图上加载GIS图层
function loadGISLayerOnMap(layer) {
    if (layer.layer_type === 'vector') {
        // 加载矢量图层
        fetch(`/api/gis-layers/${layer.id}/geojson`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const geoJsonLayer = L.geoJSON(data.data, {
                        style: function(feature) {
                            const defaultStyle = layer.default_style || {};
                            return {
                                color: defaultStyle.color || '#FF0000',
                                weight: defaultStyle.weight || 2,
                                opacity: layer.opacity || 0.8,
                                fillColor: defaultStyle.fillColor || '#FF0000',
                                fillOpacity: (layer.opacity || 0.8) * 0.3
                            };
                        },
                        onEachFeature: function(feature, layer) {
                            if (feature.properties) {
                                const popupContent = Object.entries(feature.properties)
                                    .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
                                    .join('<br>');
                                layer.bindPopup(popupContent);
                            }
                        }
                    });

                    // 添加到地图
                    geoJsonLayer.addTo(map);

                    // 存储图层引用
                    if (!mapLayers.gisLayers) {
                        mapLayers.gisLayers = {};
                    }
                    mapLayers.gisLayers[layer.id] = geoJsonLayer;

                    console.log(`GIS矢量图层 ${layer.name} 加载完成`);
                } else {
                    showAlert(`加载图层失败: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                console.error('加载GIS矢量图层失败:', error);
                showAlert('加载图层失败', 'error');
            });
    } else if (layer.layer_type === 'raster') {
        // 加载栅格图层
        const tileLayer = L.tileLayer(`/visualization/api/gis-layer-tiles/${layer.id}/{z}/{x}/{y}.png`, {
            opacity: layer.opacity || 0.7,
            attribution: layer.name
        });

        tileLayer.addTo(map);

        // 存储图层引用
        if (!mapLayers.gisLayers) {
            mapLayers.gisLayers = {};
        }
        mapLayers.gisLayers[layer.id] = tileLayer;

        console.log(`GIS栅格图层 ${layer.name} 加载完成`);
    }

    updateActiveLayersCount();
}

// 从地图移除GIS图层
function removeGISLayerFromMap(layerId) {
    if (mapLayers.gisLayers && mapLayers.gisLayers[layerId]) {
        map.removeLayer(mapLayers.gisLayers[layerId]);
        delete mapLayers.gisLayers[layerId];
        updateActiveLayersCount();
        console.log(`GIS图层 ${layerId} 已移除`);
    }
}

// 更新GIS图层透明度
function updateGISLayerOpacity(layerId, opacity) {
    const opacityValue = opacity / 100;

    if (mapLayers.gisLayers && mapLayers.gisLayers[layerId]) {
        const layer = mapLayers.gisLayers[layerId];
        layer.setOpacity(opacityValue);
    }

    // 更新显示值
    document.getElementById(`gisLayer${layerId}OpacityValue`).textContent = `${opacity}%`;
}

// 删除GIS图层
function deleteGISLayer(layerId) {
    if (confirm('确定要删除这个图层吗？此操作不可恢复。')) {
        fetch(`/api/gis-layers/${layerId}/delete`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 从地图移除
                removeGISLayerFromMap(layerId);
                // 重新加载图层列表
                loadGISLayers();
                showAlert('图层删除成功', 'success');
            } else {
                showAlert(`删除失败: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('删除图层失败:', error);
            showAlert('删除失败', 'error');
        });
    }
}

// 页面加载完成后初始化GIS图层
document.addEventListener('DOMContentLoaded', function() {
    // 检查元素是否存在再绑定事件
    const uploadBtn = document.getElementById('uploadGISLayerBtn');
    if (uploadBtn) {
        uploadBtn.addEventListener('click', function() {
            $('#gisLayerUploadModal').modal('show');
        });
    }

    const refreshBtn = document.getElementById('refreshLayersBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            loadGISLayers();
        });
    }

    // 初始加载GIS图层（如果需要）
    // loadGISLayers();
});

// 注意：switchToLayer函数已移至全局作用域，避免重复定义

// 显示图层控制面板
function showLayerControls() {
    console.log('显示图层控制面板');

    // 显示图层控制面板（已禁用）
    // $('#layerControlPanel').show();

    // 重置所有图层复选框
    $('#riskBoundariesToggle, #landuseBoundariesToggle, #adminToggle, #heatmapToggle').prop('checked', false);

    // 隐藏所有透明度控制
    $('.opacity-control').hide();

    // 清除现有图层
    Object.keys(mapLayers).forEach(layerKey => {
        if (mapLayers[layerKey] && map.hasLayer(mapLayers[layerKey])) {
            map.removeLayer(mapLayers[layerKey]);
            mapLayers[layerKey] = null;
        }
    });

    // 更新图层计数
    updateActiveLayersCount();

    console.log('图层控制面板已显示，所有图层已重置');
}

// 清除所有图层
function clearAllLayers() {
    console.log('清除所有图层');

    // 清除各种图层
    Object.keys(mapLayers).forEach(layerKey => {
        if (mapLayers[layerKey] && map.hasLayer(mapLayers[layerKey])) {
            map.removeLayer(mapLayers[layerKey]);
        }
    });

    // 重置图层计数
    updateActiveLayersCount();
}

// 显示土地利用图层
function showLanduseLayer() {
    console.log('显示土地利用图层');

    // 清除其他图层
    clearAllLayers();

    // 检查是否选择了省份
    const province = currentProvince || $('#provinceSelect').val();
    if (!province) {
        showAlert('请先选择省份', 'warning');
        updateLegend('请选择省份查看土地利用数据');
        return;
    }

    // 加载土地利用图层
    loadLanduseLayer(null, province);

    // 更新图例
    updateLegend('土地利用图层', 'landuse');
}

// 显示行政区划图层
function showAdminLayer() {
    console.log('显示行政区划图层');

    // 检查是否选择了省份
    const province = currentProvince || $('#provinceSelect').val();
    if (!province) {
        showAlert('请先选择省份', 'warning');
        updateLegend('请选择省份查看行政区划数据');
        return;
    }

    // 生成省份市级行政区划pyecharts地图
    generateAdminPyEchartsMap(province);

    // 更新图例
    updateLegend('行政区划图层', 'admin');
}

// 生成省份市级行政区划PyEcharts地图
function generateAdminPyEchartsMap(province) {
    console.log(`生成行政区划地图: ${province}`);

    showMapLoading('正在生成行政区划图...');

    // 构建API URL
    const apiUrl = `/visualization/api/generate-admin-map/${encodeURIComponent(province)}`;

    fetch(apiUrl)
        .then(response => {
            console.log('行政区划API响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('行政区划API响应数据:', data);
            if (data.success) {
                console.log('行政区划地图URL:', data.map_url);
                console.log('支持的城市数量:', data.city_count);
                console.log('城市列表:', data.cities);

                // 直接显示地图，不进行文件访问测试（因为使用的是API路由）
                displayPyEchartsMap(data.map_url);
                updateAdminMapInfo(data);
                showAlert(`${province} 行政区划图生成成功，包含 ${data.city_count} 个城市`, 'success');

                // 显示行政区划图例
                showAdminLegend();
            } else {
                console.error('行政区划地图生成失败:', data.error);
                showAlert(`行政区划图生成失败: ${data.error}`, 'error');
                showPyEchartsPlaceholder(`行政区划图生成失败: ${data.error}`, 'admin');
            }
        })
        .catch(error => {
            console.error('生成行政区划地图失败:', error);
            showAlert(`行政区划图生成失败: ${error.message}`, 'error');
            showPyEchartsPlaceholder();
        })
        .finally(() => {
            hideMapLoading();
        });
}

// 更新行政区划地图信息
function updateAdminMapInfo(data) {
    // 更新地图统计信息
    if (data.data_summary) {
        const summary = data.data_summary;
        $('#totalCities').text(summary.total_cities || 0);
        $('#mapType').text('行政区划图');
    }

    console.log('行政区划地图信息已更新:', data);
}

// 显示行政区划图例
function showAdminLegend() {
    console.log('显示行政区划图例');
    // 使用正确的updateLegend函数
    updateLegend('行政区划图例', 'admin');
}


// 更新图例
function updateLegend(title, layerType = null) {
    const legendContent = document.getElementById('legendContent');

    if (!legendContent) {
        console.warn('图例容器不存在');
        return;
    }

    // 清空现有内容
    legendContent.innerHTML = '';

    // 添加标题
    const titleElement = document.createElement('h6');
    titleElement.className = 'mb-2';
    titleElement.textContent = title;
    legendContent.appendChild(titleElement);

    // 根据图层类型添加相应的图例
    if (layerType === 'risk') {
        addRiskLegend(legendContent);
    } else if (layerType === 'admin') {
        addAdminLegend(legendContent);
    } else if (layerType === 'landuse') {
        // 使用动态数据（如果有的话）
        const dynamicData = window.currentLanduseStatistics;
        addLanduseLegend(legendContent, dynamicData);
    }
}

function addRiskLegend(container) {
    const riskLevels = [
        { level: '极低风险', color: '#d4edda' },
        { level: '低风险', color: '#28a745' },
        { level: '中风险', color: '#ffc107' },
        { level: '高风险', color: '#fd7e14' },
        { level: '极高风险', color: '#dc3545' }
    ];

    riskLevels.forEach(item => {
        const legendItem = document.createElement('div');
        legendItem.className = 'd-flex align-items-center mb-1';
        legendItem.innerHTML = `
            <div style="width: 20px; height: 20px; background-color: ${item.color}; margin-right: 8px; border-radius: 3px;"></div>
            <span style="font-size: 12px;">${item.level}</span>
        `;
        container.appendChild(legendItem);
    });
}

function addAdminLegend(container) {
    // 添加行政区划图例项
    const legendItem = document.createElement('div');
    legendItem.className = 'd-flex align-items-center mb-2';
    legendItem.innerHTML = `
        <div style="width: 20px; height: 20px; border: 2px solid #2c3e50; margin-right: 8px; border-radius: 3px; background-color: #a8e6cf;"></div>
        <span style="font-size: 12px;">市级行政区</span>
    `;
    container.appendChild(legendItem);

    // 添加说明信息
    const infoItem = document.createElement('div');
    infoItem.className = 'text-muted small mt-2';
    infoItem.innerHTML = `
        <i class="fas fa-info-circle me-1"></i>
        显示省份内所有地级市的行政边界
    `;
    container.appendChild(infoItem);
}

function addLanduseLegend(container, dynamicData = null) {
    // 如果有动态数据，使用动态数据；否则使用默认数据
    let landuseTypes = [];

    if (dynamicData && dynamicData.landuse_types) {
        const colorMapping = getLanduseColorMapping();

        // 根据实际数据生成图例项
        for (const [typeName, info] of Object.entries(dynamicData.landuse_types)) {
            const percentage = info.percentage || 0;
            if (percentage >= 0.1) { // 只显示占比大于0.1%的类型
                landuseTypes.push({
                    type: typeName,
                    color: colorMapping[typeName] || '#DDA0DD',
                    percentage: percentage
                });
            }
        }

        // 按百分比排序
        landuseTypes.sort((a, b) => b.percentage - a.percentage);
    } else {
        // 默认图例
        landuseTypes = [
            { type: '耕地', color: '#FFD700' },
            { type: '林地', color: '#228B22' },
            { type: '草地', color: '#90EE90' },
            { type: '水域', color: '#4169E1' },
            { type: '建设用地', color: '#808080' },
            { type: '其他', color: '#D3D3D3' }
        ];
    }

    landuseTypes.forEach(item => {
        const legendItem = document.createElement('div');
        legendItem.className = 'd-flex align-items-center mb-1';

        let itemHtml = `
            <div style="width: 20px; height: 20px; background-color: ${item.color}; margin-right: 8px; border-radius: 3px; border: 1px solid #ccc;"></div>
            <span style="font-size: 12px; flex: 1;">${item.type}</span>
        `;

        // 如果有百分比信息，显示百分比
        if (item.percentage !== undefined) {
            itemHtml += `<span style="font-size: 11px; color: #666; font-weight: bold;">${item.percentage}%</span>`;
        }

        legendItem.innerHTML = itemHtml;
        container.appendChild(legendItem);
    });
}






// 指北针和比例尺控制功能
class MapControls {
    constructor() {
        this.compassControl = document.getElementById('compassControl');
        this.scaleControl = document.getElementById('scaleControl');
        this.scaleText = document.getElementById('scaleText');
        this.scaleLine = document.getElementById('scaleLine');
        this.currentMapType = 'interactive';
        this.init();
    }

    init() {
        // 绑定指北针点击事件
        if (this.compassControl) {
            this.compassControl.addEventListener('click', () => {
                this.resetMapOrientation();
            });
        }

        // 初始化比例尺
        this.updateScale();
    }

    // 重置地图方向（指北针功能）
    resetMapOrientation() {
        if (this.currentMapType === 'interactive' && window.map) {
            // 对于Leaflet地图，重置到北方向
            map.setBearing && map.setBearing(0);
            showAlert('地图已重置为正北方向', 'success');
        } else {
            // 对于PyEcharts地图，显示提示信息
            showAlert('当前地图已对准正北方向', 'info');
        }
    }

    // 更新比例尺信息
    updateScale(mapType = 'interactive', province = null) {
        this.currentMapType = mapType;
        
        let scaleInfo = this.getScaleInfo(mapType, province);
        
        if (this.scaleText) {
            this.scaleText.textContent = scaleInfo.ratio;
        }
        
        if (this.scaleLine) {
            this.scaleLine.style.width = scaleInfo.lineWidth + 'px';
        }
        
        // 更新比例尺下方的距离标注
        const scaleTexts = this.scaleControl.querySelectorAll('.scale-text');
        if (scaleTexts.length > 1) {
            scaleTexts[1].textContent = scaleInfo.distance;
        }
    }

    // 获取不同地图类型的比例尺信息
    getScaleInfo(mapType, province) {
        switch(mapType) {
            case 'interactive':
                return {
                    ratio: '1:500,000',
                    lineWidth: 100,
                    distance: '0 ——— 50km'
                };
            case 'risk':
                return this.getProvinceScaleInfo(province, '风险区划');
            case 'landuse':
                return this.getProvinceScaleInfo(province, '土地利用');
            case 'admin':
                return this.getProvinceScaleInfo(province, '行政区划');
            default:
                return {
                    ratio: '1:1,000,000',
                    lineWidth: 80,
                    distance: '0 ——— 100km'
                };
        }
    }

    // 根据省份获取合适的比例尺信息
    getProvinceScaleInfo(province, mapType) {
        // 根据不同省份的大小调整比例尺
        const provinceScales = {
            '北京市': { ratio: '1:200,000', lineWidth: 120, distance: '0 ——— 20km' },
            '天津市': { ratio: '1:200,000', lineWidth: 120, distance: '0 ——— 20km' },
            '上海市': { ratio: '1:150,000', lineWidth: 120, distance: '0 ——— 15km' },
            '重庆市': { ratio: '1:800,000', lineWidth: 100, distance: '0 ——— 80km' },
            '河南省': { ratio: '1:1,200,000', lineWidth: 100, distance: '0 ——— 120km' },
            '山东省': { ratio: '1:1,000,000', lineWidth: 100, distance: '0 ——— 100km' },
            '河北省': { ratio: '1:1,200,000', lineWidth: 100, distance: '0 ——— 120km' },
            '山西省': { ratio: '1:800,000', lineWidth: 100, distance: '0 ——— 80km' },
            '江苏省': { ratio: '1:600,000', lineWidth: 100, distance: '0 ——— 60km' },
            '浙江省': { ratio: '1:600,000', lineWidth: 100, distance: '0 ——— 60km' },
            '安徽省': { ratio: '1:800,000', lineWidth: 100, distance: '0 ——— 80km' },
            '福建省': { ratio: '1:600,000', lineWidth: 100, distance: '0 ——— 60km' },
            '江西省': { ratio: '1:700,000', lineWidth: 100, distance: '0 ——— 70km' },
            '湖北省': { ratio: '1:900,000', lineWidth: 100, distance: '0 ——— 90km' },
            '湖南省': { ratio: '1:800,000', lineWidth: 100, distance: '0 ——— 80km' },
            '广东省': { ratio: '1:900,000', lineWidth: 100, distance: '0 ——— 90km' },
            '广西壮族自治区': { ratio: '1:1,000,000', lineWidth: 100, distance: '0 ——— 100km' },
            '海南省': { ratio: '1:400,000', lineWidth: 100, distance: '0 ——— 40km' },
            '四川省': { ratio: '1:1,500,000', lineWidth: 100, distance: '0 ——— 150km' },
            '贵州省': { ratio: '1:800,000', lineWidth: 100, distance: '0 ——— 80km' },
            '云南省': { ratio: '1:1,400,000', lineWidth: 100, distance: '0 ——— 140km' },
            '西藏自治区': { ratio: '1:2,000,000', lineWidth: 100, distance: '0 ——— 200km' },
            '陕西省': { ratio: '1:900,000', lineWidth: 100, distance: '0 ——— 90km' },
            '甘肃省': { ratio: '1:1,500,000', lineWidth: 100, distance: '0 ——— 150km' },
            '青海省': { ratio: '1:1,800,000', lineWidth: 100, distance: '0 ——— 180km' },
            '宁夏回族自治区': { ratio: '1:400,000', lineWidth: 100, distance: '0 ——— 40km' },
            '新疆维吾尔自治区': { ratio: '1:3,000,000', lineWidth: 100, distance: '0 ——— 300km' },
            '内蒙古自治区': { ratio: '1:2,500,000', lineWidth: 100, distance: '0 ——— 250km' },
            '辽宁省': { ratio: '1:800,000', lineWidth: 100, distance: '0 ——— 80km' },
            '吉林省': { ratio: '1:900,000', lineWidth: 100, distance: '0 ——— 90km' },
            '黑龙江省': { ratio: '1:1,500,000', lineWidth: 100, distance: '0 ——— 150km' },
            '台湾省': { ratio: '1:400,000', lineWidth: 100, distance: '0 ——— 40km' },
            '香港特别行政区': { ratio: '1:50,000', lineWidth: 120, distance: '0 ——— 5km' },
            '澳门特别行政区': { ratio: '1:20,000', lineWidth: 120, distance: '0 ——— 2km' }
        };

        return provinceScales[province] || {
            ratio: '1:1,000,000',
            lineWidth: 100,
            distance: '0 ——— 100km'
        };
    }

    // 显示控件
    show() {
        if (this.compassControl) {
            this.compassControl.style.display = 'flex';
        }
        if (this.scaleControl) {
            this.scaleControl.style.display = 'block';
        }
    }

    // 隐藏控件
    hide() {
        if (this.compassControl) {
            this.compassControl.style.display = 'none';
        }
        if (this.scaleControl) {
            this.scaleControl.style.display = 'none';
        }
    }
}

// 全局地图控件实例
let mapControls;

// 初始化时设置默认图层
document.addEventListener('DOMContentLoaded', function() {
    // 初始化地图控件
    mapControls = new MapControls();
    
    // 等待地图初始化完成后再设置默认图层
    setTimeout(function() {
        console.log('设置默认图层...');

        // 设置默认值
        if (!currentCity) {
            currentCity = 'zhengzhou';
        }
        if (!currentProvince) {
            currentProvince = '河南省';
        }

        // 默认显示交互地图
        switchToLayer('interactive');
        
        // 显示地图控件
        if (mapControls) {
            mapControls.show();
            mapControls.updateScale('interactive');
        }
    }, 1000);
});
</script>
{% endblock %}