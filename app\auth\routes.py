"""
GIS城市内涝灾害风险预警系统 - 认证路由
"""
from flask import render_template, redirect, url_for, flash, request, session, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from app.auth import bp
from app.models import User
from app.extensions import db
from app.utils import generate_captcha, validate_captcha
from app.online_users import online_tracker
from datetime import datetime, timezone, timedelta


def get_china_time():
    """获取中国本地时间（UTC+8）"""
    return datetime.now(timezone(timedelta(hours=8)))


@bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        captcha_input = request.form.get('captcha')
        remember = bool(request.form.get('remember'))

        print(f"登录尝试 - 用户名: {username}, 密码长度: {len(password) if password else 0}")

        # 验证验证码
        if not validate_captcha(captcha_input, session.get('captcha')):
            flash('验证码错误', 'error')
            print("登录失败: 验证码错误")
            return render_template('auth/login.html')

        user = User.query.filter_by(username=username).first()

        if user:
            print(f"找到用户: {user.username}, 激活状态: {user.is_active}")
            password_valid = user.check_password(password)
            print(f"密码验证结果: {password_valid}")

            if password_valid and user.is_active:
                # 更新最后登录时间
                user.last_login = get_china_time()
                db.session.commit()

                login_user(user, remember=remember)

                # 添加到在线用户跟踪器
                online_tracker.update_user_activity(
                    user_id=user.id,
                    username=user.username,
                    ip_address=request.remote_addr
                )

                print(f"用户登录成功: {user.username}")
                next_page = request.args.get('next')
                if not next_page or not next_page.startswith('/'):
                    next_page = url_for('main.dashboard')
                return redirect(next_page)
            else:
                if not password_valid:
                    flash('密码错误', 'error')
                    print("登录失败: 密码错误")
                else:
                    flash('账户已被禁用', 'error')
                    print("登录失败: 账户被禁用")
        else:
            flash('用户不存在', 'error')
            print(f"登录失败: 用户 {username} 不存在")
    
    return render_template('auth/login.html')


@bp.route('/logout')
@login_required
def logout():
    """用户登出"""
    user_id = current_user.id
    username = current_user.username

    # 从在线用户跟踪器中移除
    online_tracker.remove_user(user_id)
    print(f"用户登出: {username} (ID: {user_id}) 已从在线用户跟踪器中移除")

    logout_user()
    flash('您已成功登出', 'info')
    return redirect(url_for('main.index'))


@bp.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        captcha_input = request.form.get('captcha')

        # 验证验证码
        if not validate_captcha(captcha_input, session.get('captcha')):
            flash('验证码错误', 'error')
            return render_template('auth/register.html')

        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            flash('用户名已存在', 'error')
            return render_template('auth/register.html')
        
        # 检查邮箱是否已存在
        if User.query.filter_by(email=email).first():
            flash('邮箱已被注册', 'error')
            return render_template('auth/register.html')
        
        # 创建新用户
        user = User(username=username, email=email)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()
        
        flash('注册成功，请登录', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html')


@bp.route('/captcha')
def captcha():
    """生成验证码"""
    text, image_base64 = generate_captcha()
    session['captcha'] = text
    return jsonify({
        'image': image_base64,
        'success': True
    })


@bp.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    """找回密码"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        captcha_input = request.form.get('captcha')

        print(f"找回密码尝试 - 用户名: {username}, 邮箱: {email}")

        # 验证验证码
        if not validate_captcha(captcha_input, session.get('captcha')):
            flash('验证码错误', 'error')
            print("找回密码失败: 验证码错误")
            return render_template('auth/forgot_password.html')

        # 查找用户
        user = User.query.filter_by(username=username).first()

        if user and user.email == email:
            print(f"找到匹配用户: {user.username}, 邮箱: {user.email}")
            # 用户名和邮箱匹配，跳转到重置密码页面
            session['reset_username'] = username
            session['reset_verified'] = True
            return redirect(url_for('auth.reset_password'))
        else:
            if not user:
                flash('用户不存在', 'error')
                print(f"找回密码失败: 用户 {username} 不存在")
            else:
                flash('用户名和邮箱不匹配', 'error')
                print(f"找回密码失败: 用户 {username} 的邮箱不匹配")

    return render_template('auth/forgot_password.html')


@bp.route('/reset-password', methods=['GET', 'POST'])
def reset_password():
    """重置密码"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    # 检查是否已通过身份验证
    if not session.get('reset_verified') or not session.get('reset_username'):
        flash('请先验证身份', 'error')
        return redirect(url_for('auth.forgot_password'))

    username = session.get('reset_username')

    if request.method == 'POST':
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        captcha_input = request.form.get('captcha')

        print(f"重置密码尝试 - 用户名: {username}")

        # 验证验证码
        if not validate_captcha(captcha_input, session.get('captcha')):
            flash('验证码错误', 'error')
            print("重置密码失败: 验证码错误")
            return render_template('auth/reset_password.html', username=username)

        # 验证密码
        if len(new_password) < 6:
            flash('密码长度至少6位', 'error')
            return render_template('auth/reset_password.html', username=username)

        if new_password != confirm_password:
            flash('两次输入的密码不一致', 'error')
            return render_template('auth/reset_password.html', username=username)

        # 查找用户并更新密码
        user = User.query.filter_by(username=username).first()
        if user:
            user.set_password(new_password)
            db.session.commit()

            # 清除会话信息
            session.pop('reset_username', None)
            session.pop('reset_verified', None)

            print(f"密码重置成功: {user.username}")
            flash('密码重置成功，请使用新密码登录', 'success')
            return redirect(url_for('auth.login'))
        else:
            flash('用户不存在', 'error')
            print(f"重置密码失败: 用户 {username} 不存在")

    return render_template('auth/reset_password.html', username=username)
