/**
 * GIS城市内涝灾害风险预警系统 - 主JavaScript文件
 * Version: 1.0.1 - Fixed syntax errors
 */

// 全局配置
const GISFloodSystem = {
    config: {
        apiBaseUrl: '/api',
        mapDefaultCenter: [39.9042, 116.4074], // 北京坐标
        mapDefaultZoom: 10,
        chartTheme: 'light',
        cacheExpireTime: 30 * 60 * 1000 // 缓存过期时间：30分钟
    },

    // 数据缓存
    cache: {
        cities: null,
        floodRisks: {},
        timestamps: {}
    },
    
    // 工具函数
    utils: {
        // 显示加载状态
        showLoading: function(element) {
            if (typeof element === 'string') {
                element = document.querySelector(element);
            }
            if (element) {
                element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载中...';
                element.disabled = true;
            }
        },

        // 隐藏加载状态
        hideLoading: function(element, originalText) {
            if (typeof element === 'string') {
                element = document.querySelector(element);
            }
            if (element) {
                element.innerHTML = originalText || '确定';
                element.disabled = false;
            }
        },
        
        // 显示消息提示
        showMessage: function(message, type = 'info', duration = 3000) {
            const alertClass = type === 'error' ? 'danger' : type;
            const alertHtml = `
                <div class="alert alert-${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // 查找消息容器或创建一个
            let messageContainer = document.querySelector('.message-container');
            if (!messageContainer) {
                messageContainer = document.createElement('div');
                messageContainer.className = 'message-container';
                messageContainer.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                document.body.appendChild(messageContainer);
            }
            
            messageContainer.insertAdjacentHTML('beforeend', alertHtml);
            
            // 自动移除消息
            if (duration > 0) {
                setTimeout(() => {
                    const alerts = messageContainer.querySelectorAll('.alert');
                    if (alerts.length > 0) {
                        alerts[0].remove();
                    }
                }, duration);
            }
        },
        
        // 格式化文件大小
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        // 格式化日期时间
        formatDateTime: function(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        },
        
        // 防抖函数
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // 节流函数
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }
    },
    
    // 缓存管理
    cacheManager: {
        // 检查缓存是否有效
        isValid: function(key) {
            const timestamp = GISFloodSystem.cache.timestamps[key];
            if (!timestamp) return false;

            const now = Date.now();
            return (now - timestamp) < GISFloodSystem.config.cacheExpireTime;
        },

        // 设置缓存
        set: function(key, data) {
            GISFloodSystem.cache.timestamps[key] = Date.now();
            if (key === 'cities') {
                GISFloodSystem.cache.cities = data;
            } else if (key.startsWith('floodRisks_')) {
                const cityId = key.replace('floodRisks_', '');
                GISFloodSystem.cache.floodRisks[cityId] = data;
            }

            // 同时保存到localStorage
            try {
                localStorage.setItem(`gis_cache_${key}`, JSON.stringify({
                    data: data,
                    timestamp: Date.now()
                }));
            } catch (e) {
                console.warn('无法保存到localStorage:', e);
            }
        },

        // 获取缓存
        get: function(key) {
            // 首先检查内存缓存
            if (this.isValid(key)) {
                if (key === 'cities') {
                    return GISFloodSystem.cache.cities;
                } else if (key.startsWith('floodRisks_')) {
                    const cityId = key.replace('floodRisks_', '');
                    return GISFloodSystem.cache.floodRisks[cityId];
                }
            }

            // 检查localStorage缓存
            try {
                const cached = localStorage.getItem(`gis_cache_${key}`);
                if (cached) {
                    const parsedCache = JSON.parse(cached);
                    const now = Date.now();
                    if ((now - parsedCache.timestamp) < GISFloodSystem.config.cacheExpireTime) {
                        // 恢复到内存缓存
                        this.set(key, parsedCache.data);
                        return parsedCache.data;
                    }
                }
            } catch (e) {
                console.warn('无法从localStorage读取缓存:', e);
            }

            return null;
        },

        // 清除缓存
        clear: function(key) {
            if (key) {
                delete GISFloodSystem.cache.timestamps[key];
                if (key === 'cities') {
                    GISFloodSystem.cache.cities = null;
                } else if (key.startsWith('floodRisks_')) {
                    const cityId = key.replace('floodRisks_', '');
                    delete GISFloodSystem.cache.floodRisks[cityId];
                }
                localStorage.removeItem(`gis_cache_${key}`);
            } else {
                // 清除所有缓存
                GISFloodSystem.cache.cities = null;
                GISFloodSystem.cache.floodRisks = {};
                GISFloodSystem.cache.timestamps = {};

                // 清除localStorage中的缓存
                Object.keys(localStorage).forEach(key => {
                    if (key.startsWith('gis_cache_')) {
                        localStorage.removeItem(key);
                    }
                });
            }
        }
    },

    // API请求封装
    api: {
        // GET请求（带缓存）
        get: function(url, params = {}, useCache = true) {
            const cacheKey = url.replace('/', '_') + (Object.keys(params).length ? '_' + JSON.stringify(params) : '');

            // 如果启用缓存且缓存有效，直接返回缓存数据
            if (useCache) {
                const cachedData = GISFloodSystem.cacheManager.get(cacheKey);
                if (cachedData) {
                    return Promise.resolve(cachedData);
                }
            }

            const queryString = new URLSearchParams(params).toString();
            const fullUrl = `${GISFloodSystem.config.apiBaseUrl}${url}${queryString ? '?' + queryString : ''}`;

            return fetch(fullUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            }).then(data => {
                // 缓存响应数据
                if (useCache && data.success) {
                    GISFloodSystem.cacheManager.set(cacheKey, data);
                }
                return data;
            });
        },
        
        // POST请求
        post: function(url, data = {}) {
            return fetch(`${GISFloodSystem.config.apiBaseUrl}${url}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin',
                body: JSON.stringify(data)
            }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            });
        }
    },
    
    // 初始化函数
    init: function() {
        console.log('GIS Flood Warning System initialized');
        
        // 初始化工具提示
        this.initTooltips();
        
        // 初始化表单验证
        this.initFormValidation();
        
        // 初始化页面特定功能
        this.initPageSpecific();
    },
    
    // 初始化工具提示
    initTooltips: function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // 初始化表单验证
    initFormValidation: function() {
        // Bootstrap表单验证
        const forms = document.querySelectorAll('.needs-validation');
        Array.prototype.slice.call(forms).forEach(function (form) {
            form.addEventListener('submit', function (event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    },
    
    // 初始化页面特定功能
    initPageSpecific: function() {
        const currentPage = document.body.getAttribute('data-page');
        
        switch (currentPage) {
            case 'dashboard':
                this.initDashboard();
                break;
            case 'map':
                this.initMap();
                break;
            case 'data-management':
                this.initDataManagement();
                break;
        }
    },
    
    // 初始化仪表板
    initDashboard: function() {
        console.log('Initializing dashboard...');
        // 仪表板特定的初始化代码
    },
    
    // 初始化地图
    initMap: function() {
        console.log('Initializing map...');
        // 地图特定的初始化代码
    },
    
    // 初始化数据管理
    initDataManagement: function() {
        console.log('Initializing data management...');
        // 数据管理特定的初始化代码
    }
};

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    GISFloodSystem.init();
});

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('Global error:', e.message || e.error || e);
});

// 导出到全局作用域
window.GISFloodSystem = GISFloodSystem;
