"""
GIS城市内涝灾害风险预警系统 - 应用启动文件
"""
import os
from app import create_app, db
from app.models import User, Role
from flask.cli import with_appcontext
import click

# 获取配置环境
config_name = os.getenv('FLASK_CONFIG') or 'default'
app = create_app(config_name)


@app.cli.command()
@with_appcontext
def init_db():
    """初始化数据库"""
    click.echo('正在初始化数据库...')
    
    # 创建所有表
    db.create_all()
    
    # 创建默认角色
    if not Role.query.filter_by(name='admin').first():
        admin_role = Role(name='admin', description='系统管理员')
        db.session.add(admin_role)
    
    if not Role.query.filter_by(name='user').first():
        user_role = Role(name='user', description='普通用户')
        db.session.add(user_role)
    
    # 创建默认管理员账户
    if not User.query.filter_by(username='admin').first():
        admin_role = Role.query.filter_by(name='admin').first()
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            role=admin_role,
            is_active=True
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        click.echo('✓ 创建默认管理员账户')

    # 创建演示用户
    if not User.query.filter_by(username='demo').first():
        user_role = Role.query.filter_by(name='user').first()
        demo_user = User(
            username='demo',
            email='<EMAIL>',
            role=user_role,
            is_active=True
        )
        demo_user.set_password('demo123')
        db.session.add(demo_user)
        click.echo('✓ 创建演示用户账户')

    db.session.commit()
    click.echo('数据库初始化完成！')
    click.echo('默认账户:')
    click.echo('  管理员: admin / admin123')
    click.echo('  普通用户: demo / demo123')


@app.cli.command()
@with_appcontext
def reset_db():
    """重置数据库"""
    click.echo('正在重置数据库...')
    db.drop_all()
    db.create_all()
    click.echo('数据库重置完成！')


@app.shell_context_processor
def make_shell_context():
    """Shell上下文处理器"""
    return {
        'db': db,
        'User': User,
        'Role': Role
    }


# 注意：数据库初始化已在应用工厂函数 app/__init__.py 的 init_database() 中处理
# 这里不需要重复的初始化代码


if __name__ == '__main__':
    # 开发环境启动配置
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
